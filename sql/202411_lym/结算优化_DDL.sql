ALTER TABLE `c_customer_service_period_month`
    ADD COLUMN `prepay_status` TINYINT NOT NULL DEFAULT '1' COMMENT '预收状态，1-未预收，2-预收中，3-已预收' AFTER `sys_settlement_month`;
ALTER TABLE `c_settlement_order_data_temp`
    ADD COLUMN `period_settlement_status` TINYINT NULL DEFAULT NULL COMMENT '账期结算状态，1-不可结算，2-待结算，3-结算中，4-已结算' AFTER `period_in_account_rpa_remark`,
    ADD COLUMN `period_prepay_status` TINYINT NULL DEFAULT NULL COMMENT '账期预收状态，1-未预收，2-预收中，3-已预收' AFTER `period_settlement_status`;
ALTER TABLE `c_settlement_order_data`
    ADD COLUMN `period_settlement_status` TINYINT NULL DEFAULT NULL COMMENT '账期结算状态，1-不可结算，2-待结算，3-结算中，4-已结算' AFTER `period_in_account_rpa_remark`,
    ADD COLUMN `period_prepay_status` TINYINT NULL DEFAULT NULL COMMENT '账期预收状态，1-未预收，2-预收中，3-已预收' AFTER `period_settlement_status`;