package com.bxm.thirdpart.api.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportDeductionGetVO {

    private String taxNumber;

    private String customerName;

    private String creditCode;

    private String operator;

    private Integer period;

    private Long customerServiceId;

    private Long userId;

    private Long syncRecordId;
}
