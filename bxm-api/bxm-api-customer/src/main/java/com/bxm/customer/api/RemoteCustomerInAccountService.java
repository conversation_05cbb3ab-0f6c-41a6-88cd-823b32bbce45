package com.bxm.customer.api;

import com.bxm.common.core.constant.ServiceNameConstants;
import com.bxm.common.core.domain.R;
import com.bxm.customer.api.domain.dto.RemoteCustomerInAccountDTO;
import com.bxm.customer.api.domain.dto.RemoteCustomerServiceInAccount;
import com.bxm.customer.api.domain.vo.RemoteCustomerInAccountVO;
import com.bxm.customer.api.domain.vo.RemoteOperateInAccountRpaUpdateVO;
import com.bxm.customer.api.domain.vo.RemoteUpdateInAccountV2VO;
import com.bxm.customer.api.domain.vo.RemoteUpdateInAccountV3VO;
import com.bxm.customer.api.factory.RemoteCustomerInAccountFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客户入账交付服务
 */
@FeignClient(contextId = "remoteCustomerInAccountService", value = ServiceNameConstants.CUSTOMER_SERVICE, fallbackFactory = RemoteCustomerInAccountFallbackFactory.class)
public interface RemoteCustomerInAccountService {

    @PostMapping("/bxmCustomer/inAccount/updateInAccountV2Inner")
    R<Integer> updateInAccount(@RequestHeader("deptId") Long deptId, @RequestBody RemoteUpdateInAccountV2VO vo);

    @PostMapping("/bxmCustomer/inAccount/updateInAccountV3Inner")
    R<Integer> updateInAccountV3(@RequestHeader("deptId") Long deptId, @RequestBody RemoteUpdateInAccountV3VO vo);

    @PostMapping("/bxmCustomer/inAccount/inAccountRpaUpdateInner")
    R<Integer> inAccountRpaUpdateInner(@RequestHeader("deptId") Long deptId, @RequestBody RemoteOperateInAccountRpaUpdateVO vo);

    @GetMapping("/bxmCustomer/inAccount/getByPeriodId")
    R<RemoteCustomerServiceInAccount> getByPeriodId(@RequestParam("customerServicePeriodMonthId") Long customerServicePeriodMonthId);

    @PostMapping("/bxmCustomer/inAccount/getByPeriodIdList")
    R<List<RemoteCustomerServiceInAccount>> getByPeriodIdList(@RequestBody List<Long> periodIds);

    @PostMapping("/bxmCustomer/inAccount/inAccountListInner")
    R<List<RemoteCustomerInAccountDTO>> inAccountList(@RequestBody RemoteCustomerInAccountVO vo);
}
