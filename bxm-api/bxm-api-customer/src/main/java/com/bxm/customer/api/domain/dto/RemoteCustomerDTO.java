package com.bxm.customer.api.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 客户服务对象 c_customer_service
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@Accessors(chain = true)
public class RemoteCustomerDTO implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 客户服务id */
    @ApiModelProperty(value = "客户服务id")
    private Long id;

    /** 业务部门id */
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 服务类型 */
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /** 客户名 */
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 客户企业名 */
    @ApiModelProperty(value = "客户企业名")
    private String customerCompanyName;

    /** 信用代码 */
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 税号 */
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 服务编号 */
    @ApiModelProperty(value = "服务编号")
    private String serviceNumber;

    /** 首个账期 */
    @ApiModelProperty(value = "首个账期")
    private Integer firstAccountPeriod;

    /** 结束账期 */
    @ApiModelProperty(value = "结束账期")
    private Integer endAccountPeriod;

    /** 冻结账期 */
    @ApiModelProperty(value = "冻结账期")
    private Integer frozeAccountPeriod;

    /** 解冻账期 */
    @ApiModelProperty(value = "解冻账期")
    private Integer unfrozeAccountPeriod;

    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 顾问部门id */
    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    /** 顾问顶级部门id */
    @ApiModelProperty(value = "顾问顶级部门id")
    private Long advisorTopDeptId;

    /** 会计部门id */
    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    /** 会计顶级部门id */
    @ApiModelProperty(value = "会计顶级部门id")
    private Long accountingTopDeptId;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

    /** 是否删除，0-否，1-是 */
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    private List<String> sysAccountNames;
}
