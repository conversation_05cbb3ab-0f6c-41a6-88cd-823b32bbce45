package com.bxm.customer.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteUpdateCustomerTagVO {

    private Long customerServiceId;

    private Integer validPeriod;

    private Integer updateType;

    private List<String> tagNames;

    private Long deptId;

    private Long userId;

    private String operName;
}
