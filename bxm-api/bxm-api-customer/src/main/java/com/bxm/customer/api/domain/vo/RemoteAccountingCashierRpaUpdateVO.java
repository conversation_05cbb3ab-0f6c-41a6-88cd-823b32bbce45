package com.bxm.customer.api.domain.vo;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteAccountingCashierRpaUpdateVO {

    @ApiModelProperty("单个操作的id")
    private Long id;

    @ApiModelProperty("入账交付相关信息（仅交付操作且类型为入账需要传）")
    private RemoteInAccountDeliverVO inAccountDeliverInfo;

    @ApiModelProperty("rpa备注")
    private String rpaRemark;

    @ApiModelProperty("附件列表")
    private List<CommonFileVO> files;

    @ApiModelProperty("rpa执行结果，1-成功，0-失败")
    private Integer rpaExeResult;

    @ApiModelProperty("rpa查询时间，yyyy-MM-dd HH:mm:ss")
    private String rpaSearchTime;

    @ApiModelProperty(hidden = true)
    private Long userId;

    @ApiModelProperty(hidden = true)
    private Long deptId;

    @ApiModelProperty(hidden = true)
    private String operName;

    @ApiModelProperty(hidden = true)
    private Boolean isCoverFiles;
}
