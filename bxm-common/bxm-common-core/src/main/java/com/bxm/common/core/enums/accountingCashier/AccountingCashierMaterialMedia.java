package com.bxm.common.core.enums.accountingCashier;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountingCashierMaterialMedia {

    // 材料介质，1-电子，2-纸质，3-无，4-其他，5-银企
    ELECTRONIC(1, "电子"),
    PAPER(2, "纸质"),
    NONE(3, "无"),
    OTHER(4, "其他"),
    BANK_CARD(5, "银企"),
    UN_KNOW(99, "未知"),
    ;

    private final Integer code;
    private final String desc;

    public static AccountingCashierMaterialMedia getByCode(Integer code) {
        for (AccountingCashierMaterialMedia value : AccountingCashierMaterialMedia.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return UN_KNOW;
    }

    public static AccountingCashierMaterialMedia getByDesc(String desc) {
        for (AccountingCashierMaterialMedia value : AccountingCashierMaterialMedia.values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return UN_KNOW;
    }
}
