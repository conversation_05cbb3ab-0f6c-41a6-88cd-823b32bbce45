package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DeliverFileType {

    PERSON_CHANGE(1, "人员变动"),
    REPORT(2, "申报"),
    DEDUCTION(3, "扣款"),
    EXCEPTION_DEAL(4, "异常处理"),
    NEW_PRE_AUTH(5, "新建预认证"),
    PRE_AUTH_AUTH(6, "预认证认证"),
    NEW_NATIONAL_TAX(7, "新建国税"),
    NEW_OPERATING_INCOME_PERSON_TAX(8, "新建个税（经营收入）"),
    CHECK_REPORT(9, "检查申报"),
    PRE_AUTH_CONFIRM(10, "预认证确认附件"),
    NEW_SETTLE_ACCOUNTS(11, "新建汇算附件"),
    NEW_ANNUAL_REPORT(12, "新建年报附件"),
    DELIVER_FILE(13, "交付附件"),
    NEW_RESIDUAL_BENEFITS(14, "新建残保金附件"),
    NEW_TIMES_REPORT(15, "新建次报附件"),
    ;

    private final Integer code;

    private final String name;
}
