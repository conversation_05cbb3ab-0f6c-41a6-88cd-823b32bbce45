package com.bxm.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ServiceType {

    UN_KNOW(-1, "未知"),

    ACCOUNT(1, "代账"),
    ;

    private final Integer code;

    private final String name;

    public static ServiceType getByCode(Integer source) {
        for (ServiceType item : ServiceType.values()) {
            if (item.getCode().equals(source)) {
                return item;
            }
        }
        return UN_KNOW;
    }
}
