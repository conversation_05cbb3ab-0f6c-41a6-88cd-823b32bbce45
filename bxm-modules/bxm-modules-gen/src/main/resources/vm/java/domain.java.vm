package ${packageName}.domain;

#foreach ($import in $importList)
import ${import};
#end
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

/**
 * ${functionName}对象 ${tableName}
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@Data
@ApiModel("${functionName}对象")
@Accessors(chain = true)
@TableName("${tableName}")
public class ${ClassName} extends BaseEntity
{
    private static final long serialVersionUID = 1L;

#foreach ($column in $columns)
#if(!$table.isSuperColumn($column.javaField))
    /** $column.columnComment */
#if($column.list)
#set($parentheseIndex=$column.columnComment.indexOf("（"))
#if($parentheseIndex != -1)
#set($comment=$column.columnComment.substring(0, $parentheseIndex))
#else
#set($comment=$column.columnComment)
#end
#if($parentheseIndex != -1)
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
#elseif($column.javaType == 'LocalDateTime')
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
#elseif($column.javaType == 'LocalDate')
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
#elseif($column.javaType == 'LocalTime')
    @JsonFormat(timezone = "GMT+8", pattern = "HH:mm:ss")
    @Excel(name = "${comment}", width = 30, dateFormat = "HH:mm:ss")
#else
    @Excel(name = "${comment}")
#end
#end
#if($column.isPk == 1)
#if($column.isIncrement == 1)
    @TableId(value = "${column.columnName}", type = IdType.AUTO)
    @Excel(name = "$column.columnComment")
#else
    @TableId(value = "${column.columnName}", type = IdType.ASSIGN_ID)
    @Excel(name = "$column.columnComment")
#end
#else
    @TableField("${column.columnName}")
#end
    @ApiModelProperty(value = "${column.columnComment}")
    private $column.javaType $column.javaField;

#end
#end
#if($table.sub)
    /** $table.subTable.functionName信息 */
    @TableField(exist = false)
    private List<${subClassName}> ${subclassName}List;

#end
}
