package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.BusinessTaskFile;

/**
 * 业务任务的附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@Mapper
public interface BusinessTaskFileMapper extends BaseMapper<BusinessTaskFile>
{
    /**
     * 查询业务任务的附件
     * 
     * @param id 业务任务的附件主键
     * @return 业务任务的附件
     */
    public BusinessTaskFile selectBusinessTaskFileById(Long id);

    /**
     * 查询业务任务的附件列表
     * 
     * @param businessTaskFile 业务任务的附件
     * @return 业务任务的附件集合
     */
    public List<BusinessTaskFile> selectBusinessTaskFileList(BusinessTaskFile businessTaskFile);

    /**
     * 新增业务任务的附件
     * 
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    public int insertBusinessTaskFile(BusinessTaskFile businessTaskFile);

    /**
     * 修改业务任务的附件
     * 
     * @param businessTaskFile 业务任务的附件
     * @return 结果
     */
    public int updateBusinessTaskFile(BusinessTaskFile businessTaskFile);

    /**
     * 删除业务任务的附件
     * 
     * @param id 业务任务的附件主键
     * @return 结果
     */
    public int deleteBusinessTaskFileById(Long id);

    /**
     * 批量删除业务任务的附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBusinessTaskFileByIds(Long[] ids);
}
