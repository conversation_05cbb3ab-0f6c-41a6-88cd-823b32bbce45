package com.bxm.customer.converter;

import com.bxm.common.core.utils.DateUtils;
import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 增值员工信息转换器
 *
 * 负责VO和DO之间的数据转换，保持数据传输层和持久化层的解耦。
 * 使用BeanUtils.copyProperties简化字段复制，提高代码可维护性。
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Component
public class ValueAddedEmployeeConverter {

    /**
     * 将UpsertVO转换为DO对象
     *
     * @param vo UpsertVO对象
     * @return DO对象
     */
    public ValueAddedEmployee voToDo(ValueAddedEmployeeVO vo) {
        if (vo == null) {
            return null;
        }

        ValueAddedEmployee employee = new ValueAddedEmployee();

        // 使用BeanUtils自动复制同名字段
        BeanUtils.copyProperties(vo, employee);

        // 设置默认值
        setDefaultValues(employee);

        return employee;
    }

    /**
     * 将DO对象转换为UpsertVO
     *
     * @param employee DO对象
     * @return UpsertVO对象
     */
    public ValueAddedEmployeeVO doToVo(ValueAddedEmployee employee) {
        if (employee == null) {
            return null;
        }

        ValueAddedEmployeeVO vo = new ValueAddedEmployeeVO();

        // 使用BeanUtils自动复制同名字段
        BeanUtils.copyProperties(employee, vo);

        return vo;
    }

    /**
     * 更新DO对象的字段（用于更新操作）
     *
     * @param target 目标DO对象
     * @param source 源VO对象
     */
    public void updateDoFromVo(ValueAddedEmployee target, ValueAddedEmployeeVO source) {
        if (target == null || source == null) {
            return;
        }

        // 保存不应被覆盖的字段
        Long originalId = target.getId();
        String originalCreateBy = target.getCreateBy();
        java.time.LocalDateTime originalCreateTime = target.getCreateTime();

        // 使用BeanUtils复制所有字段
        BeanUtils.copyProperties(source, target);

        // 恢复不应被覆盖的字段
        target.setId(originalId);
        target.setCreateBy(originalCreateBy);
        target.setCreateTime(originalCreateTime);

        // 设置更新时间
        target.setUpdateTime(DateUtils.getNowDate());

        // 设置默认值（如果需要）
        setDefaultValues(target);
    }

    /**
     * 设置DO对象的默认值
     *
     * @param employee DO对象
     */
    private void setDefaultValues(ValueAddedEmployee employee) {
        if (employee.getStatus() == null) {
            employee.setStatus(1); // 默认状态：待处理
        }
    }
}
