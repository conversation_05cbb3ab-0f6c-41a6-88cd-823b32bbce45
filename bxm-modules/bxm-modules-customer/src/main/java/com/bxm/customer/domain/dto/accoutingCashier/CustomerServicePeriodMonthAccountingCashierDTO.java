package com.bxm.customer.domain.dto.accoutingCashier;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServicePeriodMonthAccountingCashierDTO {

    @ApiModelProperty("账务交付单状态")
    private Integer status;

    @ApiModelProperty("账务交付单状态文案")
    private String statusStr;

    @ApiModelProperty("是否有交付单（是否有链接）")
    private Boolean hasAccountingCashier;

    @ApiModelProperty("账务交付单id，目前只有账期列表上的入账会返回这个值，因为只会有一个，其他都需要额外再请求一下")
    private Long accountingCashierId;
}
