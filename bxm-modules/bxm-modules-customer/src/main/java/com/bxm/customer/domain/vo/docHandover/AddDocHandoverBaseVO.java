package com.bxm.customer.domain.vo.docHandover;

import com.bxm.common.core.web.domain.CommonFileVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/6 9:23
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AddDocHandoverBaseVO {
    @ApiModelProperty(value = "客户企业ID")
    @NotNull(message = "请选择客户企业")
    private Long customerServiceId;

    /*@ApiModelProperty(value = "客户名")
    @NotEmpty(message = "请选择客户企业")
    private String customerName;

    @ApiModelProperty(value = "信用代码")
    @NotEmpty(message = "请选择客户企业")
    private String creditCode;*/

    @ApiModelProperty(value = "月度账期id")
    @NotNull(message = "请选择账期")
    private Long customerServicePeriodMonthId;

    @ApiModelProperty(value = "账期")
    @NotNull(message = "请选择账期")
    private Integer period;

    @ApiModelProperty(value = "凭票入账:1-是/0-否")
    @NotNull(message = "请选择凭票入账")
    private Integer isVoucherEntry;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("附件")
    private List<CommonFileVO> files;

    /**
     * 从哪里创建的
     * 2：从补账创建的
     */
    private Integer addFromType;

    /**
     * 从addFromType创建的 addFromType的ID
     */
    private Long addFromId;
}
