package com.bxm.customer.domain.dto.materialDeliver;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverBankDetailDTO {

    @ApiModelProperty("文件名称")
    @Excel(name = "文件名")
    private String fileName;

    @ApiModelProperty("解析结果(文案)")
    @Excel(name = "解析结果")
    private String analysisResultStr;

    @ApiModelProperty("异常原因")
    @Excel(name = "异常原因")
    private String errorMsg;

    @ApiModelProperty("客户名称")
    @Excel(name = "归属客户")
    private String customerName;

    @ApiModelProperty("银行信息")
    @Excel(name = "银行")
    private String bankInfo;

    @ApiModelProperty("文件备注")
    @Excel(name = "文件备注")
    private String fileRemark;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "开始时间", dateFormat = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Excel(name = "结束时间", dateFormat = "yyyy-MM-dd")
    private LocalDate endDate;
}
