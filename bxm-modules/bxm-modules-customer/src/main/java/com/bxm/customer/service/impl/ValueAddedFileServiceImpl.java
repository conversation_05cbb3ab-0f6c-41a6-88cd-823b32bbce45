package com.bxm.customer.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.customer.domain.ValueAddedFile;
import com.bxm.customer.domain.dto.FileStatusInfo;
import com.bxm.customer.domain.enums.ValueAddedFileType;
import com.bxm.customer.domain.enums.ValueAddedProcessStatus;
import com.bxm.customer.mapper.ValueAddedFileMapper;
import com.bxm.customer.service.IValueAddedFileService;
import com.bxm.file.api.RemoteFileService;
import com.bxm.file.api.domain.RemoteAliFileDTO;
import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

/**
 * 增值交付单文件Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Slf4j
@Service
public class ValueAddedFileServiceImpl extends ServiceImpl<ValueAddedFileMapper, ValueAddedFile>
        implements IValueAddedFileService {

    @Autowired
    private RemoteFileService remoteFileService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long savePersonnelExcelFile(MultipartFile excelFile, String deliveryOrderNo) throws Exception {
        log.info("Saving personnel excel file, fileName: {}, size: {}", excelFile.getOriginalFilename(), excelFile.getSize());

        // 1. 文件大小验证
        if (excelFile.getSize() > 2 * 1024 * 1024) { // 2MB限制
            throw new IllegalArgumentException("Excel文件大小不能超过2MB");
        }

        // 2. 上传文件到文件服务
        RemoteAliFileDTO uploadResult = remoteFileService.uploadFile(excelFile).getData();
        if (uploadResult == null) {
            throw new RuntimeException("文件上传失败");
        }

        // 3. 保存文件记录到数据库
        ValueAddedFile fileRecord = new ValueAddedFile();
        fileRecord.setDeliveryOrderNo(deliveryOrderNo);
        fileRecord.setFileName(excelFile.getOriginalFilename());
        fileRecord.setFileUrl(uploadResult.getUrl());
        fileRecord.setFileSize(excelFile.getSize());
        fileRecord.setFileType(ValueAddedFileType.PERSONNEL_EXCEL.getCode()); // 人员明细excel
        fileRecord.setIsDel(false);
        fileRecord.setCreateTime(LocalDateTime.now());
        fileRecord.setUpdateTime(LocalDateTime.now());

        // 初始化处理状态 - 使用统一的工具方法构建JSON
        String initialStatus = buildInitialStatusJson();
        fileRecord.setStatus(ValueAddedProcessStatus.PROCESSING.getCode());
        fileRecord.setRemark(initialStatus);

        if (!save(fileRecord)) {
            throw new RuntimeException("保存文件记录失败");
        }

        log.info("File record saved successfully, fileId: {}, fileUrl: {}", fileRecord.getId(), uploadResult.getUrl());

        return fileRecord.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFileProcessStatus(Long fileId, Integer status, String statusInfo) {
        log.info("Updating file process status, fileId: {}, status: {}", fileId, status);

        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            log.warn("File record not found, fileId: {}", fileId);
            return;
        }

        // 更新状态字段和备注信息
        fileRecord.setStatus(status);
        fileRecord.setRemark(statusInfo);
        fileRecord.setUpdateTime(LocalDateTime.now());

        if (!updateById(fileRecord)) {
            log.error("Failed to update file process status, fileId: {}", fileId);
            throw new RuntimeException("更新文件处理状态失败");
        }

        log.info("File process status updated successfully, fileId: {}, status: {}", fileId, status);
    }

    @Override
    public String getFileProcessStatus(Long fileId) {
        ValueAddedFile fileRecord = getById(fileId);
        if (fileRecord == null) {
            return buildErrorStatusJson("文件记录不存在");
        }
        return fileRecord.getRemark();
    }

    @Override
    public ValueAddedFile getFileById(Long fileId) {
        return getById(fileId);
    }

    /**
     * 构建初始状态JSON
     * 使用FileStatusInfo DTO统一处理
     */
    private String buildInitialStatusJson() {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.PROCESSING.getCode())
                    .message("文件已上传，等待处理")
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            // 使用FastJSON2替代ObjectMapper
            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build initial status JSON failed", e);
            return "{\"status\":0,\"message\":\"文件已上传，等待处理\"}";
        }
    }

    /**
     * 构建错误状态JSON
     * 用于文件记录不存在等错误情况
     */
    private String buildErrorStatusJson(String message) {
        try {
            FileStatusInfo statusInfo = FileStatusInfo.builder()
                    .status(ValueAddedProcessStatus.FAILED.getCode())
                    .message(message)
                    .totalCount(0)
                    .successCount(0)
                    .failCount(0)
                    .hasErrorFile(false)
                    .updateTime(System.currentTimeMillis())
                    .build();

            return JSON.toJSONString(statusInfo);
        } catch (Exception e) {
            log.error("Build error status JSON failed", e);
            return "{\"status\":2,\"message\":\"" + message + "\"}";
        }
    }
}
