package com.bxm.customer.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.customer.domain.NewCustomerTransferFile;

/**
 * 新户流转客户文件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Mapper
public interface NewCustomerTransferFileMapper extends BaseMapper<NewCustomerTransferFile>
{
    /**
     * 查询新户流转客户文件
     * 
     * @param id 新户流转客户文件主键
     * @return 新户流转客户文件
     */
    public NewCustomerTransferFile selectNewCustomerTransferFileById(Long id);

    /**
     * 查询新户流转客户文件列表
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 新户流转客户文件集合
     */
    public List<NewCustomerTransferFile> selectNewCustomerTransferFileList(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 新增新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    public int insertNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 修改新户流转客户文件
     * 
     * @param newCustomerTransferFile 新户流转客户文件
     * @return 结果
     */
    public int updateNewCustomerTransferFile(NewCustomerTransferFile newCustomerTransferFile);

    /**
     * 删除新户流转客户文件
     * 
     * @param id 新户流转客户文件主键
     * @return 结果
     */
    public int deleteNewCustomerTransferFileById(Long id);

    /**
     * 批量删除新户流转客户文件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteNewCustomerTransferFileByIds(Long[] ids);
}
