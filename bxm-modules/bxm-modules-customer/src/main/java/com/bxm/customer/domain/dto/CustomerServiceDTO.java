package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.bxm.customer.domain.CustomerSysAccount;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceDTO {

    @ApiModelProperty("客户服务id")
    private Long id;

    @ApiModelProperty("客户名称")
    @Excel(name = "客户名称")
    private String customerName;

    @ApiModelProperty("客户企业名称")
    @Excel(name = "客户企业名称")
    private String customerCompanyName;

    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("服务编号")
    @Excel(name = "服务编号")
    private String serviceNumber;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    @ApiModelProperty("纳税人性质，1-小规模，2-一般纳税人")
    @Excel(name = "服务纳税人性质")
    private String taxTypeStr;

    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
//    @Excel(name = "服务状态", readConverterExp = "1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

//    @Excels({
//            @Excel(name = "标签名称", targetAttr = "tagName", type = Excel.Type.EXPORT),
//    })
    @ApiModelProperty("标签")
    private List<TagDTO> tagList;

    @ApiModelProperty("是否零申报")
    @Excel(name = "零申报")
    private String isLsb;

    @ApiModelProperty("是否三零")
    @Excel(name = "三零")
    private String isSl;

    @Excel(name = "服务标签")
    private String tagNames;

    @ApiModelProperty(value = "服务状态文案")
    @Excel(name = "服务状态")
    private String serviceStatusStr;

    @ApiModelProperty("开始账期")
    @Excel(name = "开始账期")
    private Integer startPeriod;

    @ApiModelProperty("结束账期")
    @Excel(name = "结束账期")
    private Integer endPeriod;

    @ApiModelProperty("业务公司名称")
    @Excel(name = "业务公司")
    private String businessDeptName;

    @ApiModelProperty("业务公司id")
    private Long businessDeptId;

    @ApiModelProperty("顾问")
    @Excel(name = "顾问")
    private String advisorInfo;

    private Long advisorDeptId;

    private String advisorDeptName;

    @ApiModelProperty("顾问员工")
    private List<ServiceEmployeeDTO> advisorEmployees;

    private String advisorEmployeeNames;

    @ApiModelProperty("会计员工")
    private List<ServiceEmployeeDTO> accountingEmployees;

    private String accountingEmployeeNames;

    @ApiModelProperty("会计")
    @Excel(name = "会计")
    private String accountingInfo;

    private Long accountingDeptId;

    private String accountingDeptName;

    @ApiModelProperty("开票取数截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime ticketTime;

    @ApiModelProperty("开票取数截止时间")
    @Excel(name = "收入更新时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String ticketTimeStr;

    @ApiModelProperty("近12个月收入")
    private BigDecimal this12MonthIncome;

    private Integer this12MonthIncomeRpaResult;

    @ApiModelProperty("本年收入")
    private BigDecimal thisYearIncome;

    private Integer thisYearIncomeRpaResult;

    @ApiModelProperty("本季收入")
    private BigDecimal thisSeasonIncome;

    private Integer thisSeasonIncomeRpaResult;

    @ApiModelProperty("本月收入")
    private BigDecimal thisMonthIncome;

    private Integer thisMonthIncomeRpaResult;

    @ApiModelProperty("近12个月收入")
    @Excel(name = "近12个月收入")
    private String this12MonthIncomeStr;

    @ApiModelProperty("本年收入")
    @Excel(name = "本年收入")
    private String thisYearIncomeStr;

    @ApiModelProperty("本季收入")
    @Excel(name = "本季收入")
    private String thisSeasonIncomeStr;

    @ApiModelProperty("本月收入")
    @Excel(name = "本月收入")
    private String thisMonthIncomeStr;

    @ApiModelProperty("利润更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime profitGetTime;

    @ApiModelProperty("利润更新时间")
    @Excel(name = "利润更新时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private String profitGetTimeStr;

    @ApiModelProperty("最后取数账期")
    @Excel(name = "最后取数账期")
    private Integer lastInAccountPeriod;

    @ApiModelProperty(value = "上年可弥补损益")
    @Excel(name = "上年可弥补损益")
    private String lastYearDeductible;

    @ApiModelProperty(value = "上年暂估未冲")
    @Excel(name = "上年暂估未冲")
    private String priorYearEstimationNotReversed;

    @ApiModelProperty(value = "上年折旧调整")
    @Excel(name = "上年折旧调整")
    private String priorYearDepreciationAdjustment;

    @ApiModelProperty("本年累计主营收入")
    private BigDecimal mainIncome;

    @Excel(name = "本年累计主营收入")
    private String mainIncomeStr;

    @ApiModelProperty("本年累计主营成本")
    private BigDecimal mainCost;

    @Excel(name = "本年累计主营成本")
    private String mainCostStr;

    @ApiModelProperty("本年累计净利润")
    private BigDecimal profit;

    @Excel(name = "本年累计净利润")
    private String profitStr;

    @ApiModelProperty(value = "本年费用调增")
    @Excel(name = "本年费用调增")
    private String priorYearExpenseIncrease;

    @ApiModelProperty(value = "汇算利润")
    @Excel(name = "汇算利润")
    private String settlementProfit;

    @ApiModelProperty(value = "个税申报人数")
    @Excel(name = "个税申报人数")
    private Integer taxReportCount;

    @ApiModelProperty(value = "本年个税申报工资总额")
    private BigDecimal taxReportSalaryTotal;

    @ApiModelProperty(value = "本年个税申报工资总额")
    @Excel(name = "本年个税申报工资总额")
    private String taxReportSalaryTotalStr;

    @ApiModelProperty("最后结账月")
//    @Excel(name = "最后结账月")
    private String lastBillingMonth;

    @ApiModelProperty(value = "全年结账，1-待完成，2-已完成")
    private Integer fullYearClosing;

    @ApiModelProperty(value = "全年结账，文案")
//    @Excel(name = "全年结账")
    private String fullYearClosingStr;

    @ApiModelProperty("业务集团id")
    private Long businessTopDeptId;

    private String businessTopDeptName;

    @ApiModelProperty("会计集团id")
    private Long accountingLeaderDeptId;

    @ApiModelProperty("会计区域id")
    private Long accountingTopDeptId;

    private String accountingTopDeptName;

//    @Excel(name = "开票取数截止时间")
//    private String ticketTimeStr;

    @ApiModelProperty("甲方")
    private String advisorTopDeptName;

    @ApiModelProperty("会计备注")
    @Excel(name = "会计备注")
    private String accountingRemark;

    @ApiModelProperty("顾问备注")
    @Excel(name = "顾问备注")
    private String advisorRemark;

    @ApiModelProperty("医社保事项备注")
    @Excel(name = "医社保事项备注")
    private String medicalSocialMattersNotes;

    @ApiModelProperty("个税（工资薪金）事项备注")
    @Excel(name = "个税（工资薪金）事项备注")
    private String personTaxMattersNotes;

    @ApiModelProperty("个税（经营所得）事项备注")
    @Excel(name = "个税（经营所得）事项备注")
    private String operatingIncomeMattersNotes;

    @ApiModelProperty("国税事项备注")
    @Excel(name = "国税事项备注")
    private String nationalTaxMattersNotes;

    @ApiModelProperty("预认证事项备注")
    @Excel(name = "预认证事项备注")
    private String preAuthMattersNotes;

    @ApiModelProperty("流水事项备注")
    @Excel(name = "流水事项备注")
    private String bankFlowMattersNotes;

    @ApiModelProperty("入账事项备注")
    @Excel(name = "入账事项备注")
    private String inAccountMattersNotes;

    @ApiModelProperty("银行列表")
    private List<CustomerServiceBankDTO> bankList;

    @ApiModelProperty("银行列表（业务公司）")
    private List<CustomerServiceBankBusinessDTO> bankBusinessList;

    @ApiModelProperty("税种列表")
    private List<CustomerTaxTypeCheckDTO> taxTypeCheckList;

    @ApiModelProperty("税种列表（业务公司")
    private List<CustomerTaxTypeCheckBusinessDTO> taxTypeCheckBusinessList;

    @ApiModelProperty("系统账号列表")
    private List<CustomerSysAccountDTO> sysAccountList;

    @ApiModelProperty("系统账号列表（业务公司")
    private List<CustomerSysAccountBusinessDTO> sysAccountBusinessList;

    private LocalDateTime createTime;
}
