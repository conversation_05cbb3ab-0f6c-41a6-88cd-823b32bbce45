package com.bxm.customer.domain.dto.materialDeliver;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MaterialDeliverDTO {

    @ApiModelProperty("交接单id")
    private Long id;

    @ApiModelProperty("交接单编号")
    @Excel(name = "交接单编号")
    private String materialDeliverNumber;

    @ApiModelProperty("标题")
    @Excel(name = "标题")
    private String title;

    @ApiModelProperty("交接单类型，1-银行流水，2-普通入账，3-凭票入账")
    private Integer materialDeliverType;

    @ApiModelProperty("交接单类型名称")
    @Excel(name = "类型")
    private String materialDeliverTypeStr;

    @ApiModelProperty("解析状态，0-队列中，1-解析中，2-解析完成，3-解析失败，4-解析中止")
    private Integer materialDeliverAnalysisStatus;

    @ApiModelProperty("解析状态名称")
    @Excel(name = "解析状态")
    private String materialDeliverAnalysisStatusStr;

    @ApiModelProperty("解析结果，1-正常，2-异常")
    private Integer materialDeliverAnalysisResult;

    @ApiModelProperty("解析结果名称")
    @Excel(name = "解析结果")
    private String materialDeliverAnalysisResultStr;

    @ApiModelProperty("推送状态，1-待推送，2-已推送")
    private Integer materialDeliverPushStatus;

    @ApiModelProperty("推送状态名称")
    @Excel(name = "推送状态")
    private String materialDeliverPushStatusStr;

    @ApiModelProperty("提交部门id")
    private Long commitDeptId;

    @ApiModelProperty("提交人")
    private String commitUserNickName;

    @ApiModelProperty("提交小组")
    @Excel(name = "提交小组")
    private String commitInfo;

    @ApiModelProperty("提交时间")
    private LocalDateTime createTime;

    @ApiModelProperty("提交时间 （格式化后的）")
    @Excel(name = "提交时间")
    private String createTimeStr;

    @ApiModelProperty("最后操作人")
    @Excel(name = "最后操作人")
    private String lastOperName;

    @ApiModelProperty("最后操作")
    @Excel(name = "最后操作")
    private String lastOperType;

    @ApiModelProperty("最后操作时间")
    private LocalDateTime lastOperTime;

    @ApiModelProperty("最后操作时间（格式化后的）")
    @Excel(name = "最后操作时间")
    private String lastOperTimeStr;

    @ApiModelProperty("能否推送，false-否，true-能")
    private Boolean canPush;
}
