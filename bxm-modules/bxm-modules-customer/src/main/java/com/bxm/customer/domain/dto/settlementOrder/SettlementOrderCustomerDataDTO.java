package com.bxm.customer.domain.dto.settlementOrder;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementOrderCustomerDataDTO {

    @ApiModelProperty(value = "服务名称")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty(value = "信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty(value = "服务标签")
    @Excel(name = "服务标签")
    private String customerServiceTags;

    @ApiModelProperty(value = "服务纳税人性质")
    @Excel(name = "纳税人性质")
    private String customerServiceTaxType;

    @ApiModelProperty(value = "服务顾问信息")
    @Excel(name = "服务顾问")
    private String customerServiceAdvisorInfo;

    @ApiModelProperty(value = "服务会计信息")
    @Excel(name = "服务会计")
    private String customerServiceAccountingInfo;

    @ApiModelProperty(value = "服务首个账期")
    @Excel(name = "首个账期")
    private String customerServiceFirstAccountPeriod;

    @ApiModelProperty(value = "创建人")
    @Excel(name = "创建人")
    private String createInfo;

    @Excel(name = "创建时间")
    private String createTime;
}
