package com.bxm.customer.domain.dto.newCustomerTransfer;

import com.bxm.customer.domain.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NewCustomerTransferAllInfoDTO {

    private NewCustomerInfo newCustomerInfo;

    private NewCustomerAnnualReportInfo newCustomerAnnualReportInfo;

    private List<NewCustomerBankAccount> newCustomerBankAccountList;

    private NewCustomerFinanceTaxInfo newCustomerFinanceTaxInfo;

    private List<NewCustomerFixedAssetsInfo> newCustomerFixedAssetsInfoList;

    private List<NewCustomerIncomeInfo> newCustomerIncomeInfoList;

    private NewCustomerInsuranceFundInfo newCustomerInsuranceFundInfo;

    private List<NewCustomerInsuranceFundStatus> newCustomerInsuranceFundStatusList;

    private NewCustomerOtherInfo newCustomerOtherInfo;

    private List<NewCustomerSysAccount> newCustomerSysAccountList;

    private List<NewCustomerTaxTypeCheck> newCustomerTaxTypeCheckList;

    private List<CBusinessTagRelation> tagRelations;

    private String serviceNumber;
}
