package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 第三方补充同步表 c_open_api_supplement_record
 * 
 * <AUTHOR>
 * @date 2024-08-23
 */
@Data
@ApiModel("第三方扣款同步表")
@Accessors(chain = true)
@TableName("c_open_api_deduction_record")
public class OpenApiDeductionRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 来源类型，1-鑫启易 */
    @Excel(name = "来源类型，1-鑫启易")
    @TableField("source_type")
    @ApiModelProperty(value = "来源类型，1-鑫启易")
    private Integer sourceType;

    /** 批次号 */
    @Excel(name = "批次号")
    @TableField("batch_no")
    @ApiModelProperty(value = "批次号")
    private String batchNo;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 客户名称 */
    @Excel(name = "客户名称")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /** 申报期 */
    @Excel(name = "申报期")
    @TableField("report_period")
    @ApiModelProperty(value = "申报期")
    private String reportPeriod;

    /** 税局下载的报表名称 */
    @Excel(name = "税局下载的报表名称")
    @TableField("offical_filename")
    @ApiModelProperty(value = "税局下载的报表名称")
    private String officalFilename;

    @Excel(name = "税款所属期起")
    @TableField("tax_period_start")
    @ApiModelProperty(value = "税款所属期起")
    private String taxPeriodStart;

    @Excel(name = "税款所属期止")
    @TableField("tax_period_end")
    @ApiModelProperty(value = "税款所属期止")
    private String taxPeriodEnd;

    @Excel(name = "文件url")
    @TableField("file_id")
    @ApiModelProperty(value = "文件url")
    private String fileId;

    @Excel(name = "文件名称")
    @TableField("file_name")
    @ApiModelProperty(value = "文件名称")
    private String fileName;

    @TableField("deliver_id")
    @ApiModelProperty(value = "交付单id")
    @Excel(name = "交付单id")
    private Long deliverId;

    @TableField("result")
    @ApiModelProperty(value = "扣款结果，1-正常，2-异常")
    @Excel(name = "扣款结果，1-正常，2-异常")
    private String result;

    @TableField("error_result")
    @ApiModelProperty(value = "错误原因")
    @Excel(name = "错误原因")
    private String errorResult;
}
