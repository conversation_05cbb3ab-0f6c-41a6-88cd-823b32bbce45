package com.bxm.customer.domain.dto.valueAdded;

import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 个税明细导出DTO
 * 
 * 用于导出个税明细Excel文件，包含方式、姓名、身份证号、手机号、备注、
 * 应发工资、公积金个人缴存金额、社保基数、养老保险、失业保险、工伤保险、
 * 医疗保险、生育保险、其他等字段
 *
 * <AUTHOR>
 * @date 2025-01-29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("个税明细导出DTO")
public class PersonalTaxDetailExportDTO {

    /** 方式 */
    @Excel(name = "方式", sort = 1)
    @ApiModelProperty(value = "方式")
    private String operationType;

    /** 姓名 */
    @Excel(name = "姓名", sort = 2)
    @ApiModelProperty(value = "姓名")
    private String employeeName;

    /** 身份证号 */
    @Excel(name = "身份证号", sort = 3)
    @ApiModelProperty(value = "身份证号")
    private String idNumber;

    /** 手机号 */
    @Excel(name = "手机号", sort = 4)
    @ApiModelProperty(value = "手机号")
    private String mobile;

    /** 备注 */
    @Excel(name = "备注", sort = 5)
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 应发工资 */
    @Excel(name = "应发工资", sort = 6)
    @ApiModelProperty(value = "应发工资")
    private String grossSalary;

    /** 公积金个人缴存金额 */
    @Excel(name = "公积金个人缴存金额", sort = 7)
    @ApiModelProperty(value = "公积金个人缴存金额")
    private String housingFundPersonalAmount;

    /** 社保基数 */
    @Excel(name = "社保基数", sort = 8)
    @ApiModelProperty(value = "社保基数")
    private String socialInsuranceBase;

    /** 养老保险 */
    @Excel(name = "养老保险", sort = 9)
    @ApiModelProperty(value = "养老保险")
    private String yangLaoInsurance;

    /** 失业保险 */
    @Excel(name = "失业保险", sort = 10)
    @ApiModelProperty(value = "失业保险")
    private String shiYeInsurance;

    /** 工伤保险 */
    @Excel(name = "工伤保险", sort = 11)
    @ApiModelProperty(value = "工伤保险")
    private String gongShangInsurance;

    /** 医疗保险 */
    @Excel(name = "医疗保险", sort = 12)
    @ApiModelProperty(value = "医疗保险")
    private String yiLiaoInsurance;

    /** 生育保险 */
    @Excel(name = "生育保险", sort = 13)
    @ApiModelProperty(value = "生育保险")
    private String shengYuInsurance;

    /** 其他 */
    @Excel(name = "其他", sort = 14)
    @ApiModelProperty(value = "其他")
    private String other;
}
