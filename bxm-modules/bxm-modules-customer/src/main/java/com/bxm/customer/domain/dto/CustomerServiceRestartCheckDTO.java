package com.bxm.customer.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerServiceRestartCheckDTO {

    @ApiModelProperty("是否需要补账")
    private Boolean needRepairAccount;

    @ApiModelProperty("补账账期数量")
    private Integer repairAccountCount;
}
