package com.bxm.customer.domain.dto.settlementOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SettlementPushReviewOrderDTO {

    @ApiModelProperty("结算单id")
    private Long settlementOrderId;

    @ApiModelProperty("结算单标题")
    private String settlementOrderTitle;

    @ApiModelProperty("结算类型，1-入账结算，2-新户预收")
    private Integer settlementType;

    @ApiModelProperty("结算类型名称")
    private String settlementTypeName;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("结算单数据总数")
    private Long dataCount;

    @ApiModelProperty("单价")
    private BigDecimal price;

    @ApiModelProperty("结算单总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty("结算单优惠金额")
    private BigDecimal discountPrice;

    @ApiModelProperty("结算金额")
    private BigDecimal settlementPrice;

    @ApiModelProperty("是否待编辑")
    private Boolean isWaitForEdit;
}
