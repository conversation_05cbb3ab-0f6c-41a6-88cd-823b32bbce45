package com.bxm.customer.validation;

import com.bxm.common.core.utils.StringUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 社保套餐信息验证器
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public class SocialInsurancePackageValidator implements ConstraintValidator<SocialInsurancePackage, String> {

    private boolean allowEmpty;

    @Override
    public void initialize(SocialInsurancePackage constraintAnnotation) {
        this.allowEmpty = constraintAnnotation.allowEmpty();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        // 如果允许为空且值为空，则验证通过
        if (allowEmpty && StringUtils.isEmpty(value)) {
            return true;
        }

        // 如果不允许为空且值为空，则验证失败
        if (!allowEmpty && StringUtils.isEmpty(value)) {
            return false;
        }

        try {
            // 验证是否包含必要的字段：yang_lao, shi_ye, gong_shang, yi_liao, sheng_yu
            if (!value.contains("yang_lao") ||
                !value.contains("shi_ye") ||
                !value.contains("gong_shang") ||
                !value.contains("yi_liao") ||
                !value.contains("sheng_yu")) {
                return false;
            }

            // 可以在这里添加更严格的JSON格式验证
            // 例如使用JSON解析器验证格式是否正确

            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
