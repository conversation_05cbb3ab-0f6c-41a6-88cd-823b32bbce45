package com.bxm.customer.domain.dto;

import com.bxm.common.core.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerDeliverPersonalTaxExportDTO {

    @ApiModelProperty("客户名")
    @Excel(name = "客户名")
    private String customerName;

    @ApiModelProperty("信用代码")
    @Excel(name = "信用代码")
    private String creditCode;

    @ApiModelProperty("账期")
    private Integer period;

    @Excel(name = "账期")
    @ApiModelProperty("账期")
    private String periodStr;

    @Excel(name = "ddl")
    private String ddlStr;

    private Long advisorDeptId;

    private Long accountingDeptId;

    private String advisorDeptName;

    private String accountingDeptName;

    @ApiModelProperty("账期业务公司")
    @Excel(name = "账期归属业务公司")
    private String businessDeptName;

    @Excel(name = "顾问")
    @ApiModelProperty("服务顾问")
    private String advisorDeptInfo;

    @Excel(name = "会计")
    @ApiModelProperty("服务会计")
    private String accountingDeptInfo;

    /** 提交人名称 */
    @Excel(name = "提交人")
    @ApiModelProperty(value = "提交人")
    private String employeeName;

    @Excel(name = "提交时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty("提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @Excel(name = "事项备忘")
    @ApiModelProperty(value = "事项备忘")
    private String mattersNotesDetail;

    @ApiModelProperty("交付类型名称")
    @Excel(name = "交付类型")
    private String deliverTypeName;

    @Excel(name = "人员变动")
    @ApiModelProperty(value = "人员变动")
    private String hasPersonChange;

    @Excel(name = "人员变动备注")
    @ApiModelProperty(value = "人员变动信息")
    private String personChangeInfo;

    @Excel(name = "交付单附件")
    private Long createFileCount;

    @ApiModelProperty(value = "本期金额")
    @Excel(name = "本期")
    private String currentPeriodAmount;

    @ApiModelProperty(value = "逾期金额")
    @Excel(name = "逾期")
    private String overdueAmount;

    @ApiModelProperty(value = "补缴金额")
    @Excel(name = "补缴")
    private String supplementAmount;

    /** 申报金额 */
    @Excel(name = "总扣缴金额")
    @ApiModelProperty(value = "金额")
    private String reportAmount;

    @ApiModelProperty("状态")
    @Excel(name = "个税状态")
    private String statusStr;

    @Excel(name = "最后操作")
    private String lastOperType;

    @Excel(name = "最后操作时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastOperTime;

    @Excel(name = "最后操作人")
    private String lastOperName;

    @Excel(name = "最后操作备注")
    private String lastOperRemark;

    @Excel(name = "申报备注")
    private String reportRemark;

    @Excel(name = "申报附件")
    @ApiModelProperty("申报文件数量")
    private Long reportFileCount;

//    @Excel(name = "确认备注")
//    private String confirmRemark;
//
//    @Excel(name = "确认附件")
//    private Long confirmFileCount;

    @Excel(name = "扣款备注")
    private String deductionRemark;

    @Excel(name = "扣款附件")
    @ApiModelProperty("扣款文件数量")
    private Long deductionFileCount;
}
