package com.bxm.customer.domain.dto.repairAccount;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/7/20 19:41
 * happy coding!
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FirstPeriodDTO {
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    @ApiModelProperty(value = "客户名")
    private String customerName;

    @ApiModelProperty(value = "首个账期")
    private Integer firstAccountPeriod;

    @ApiModelProperty(value = "首个账期 文案")
    private String firstAccountPeriodStr;
}
