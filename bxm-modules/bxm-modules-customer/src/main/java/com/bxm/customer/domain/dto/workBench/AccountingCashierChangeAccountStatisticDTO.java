package com.bxm.customer.domain.dto.workBench;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class AccountingCashierChangeAccountStatisticDTO {

    @ApiModelProperty("待交付数量")
    private Long waitDeliverCount;

    @ApiModelProperty("异常数量")
    private Long exceptionCount;

    @ApiModelProperty("交付待提交数量")
    private Long waitSubmitCount;

    public AccountingCashierChangeAccountStatisticDTO() {
        this.waitDeliverCount = 0L;
        this.exceptionCount = 0L;
        this.waitSubmitCount = 0L;
    }
}
