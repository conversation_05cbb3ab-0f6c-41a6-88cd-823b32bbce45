package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.bxm.common.core.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.bxm.common.core.web.domain.BaseEntity;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户服务对象 c_customer_service
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("客户服务对象")
@Accessors(chain = true)
@TableName("c_customer_service")
public class CCustomerService extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 客户服务id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "客户服务id")
    @ApiModelProperty(value = "客户服务id")
    private Long id;

    /** 业务部门id */
    @Excel(name = "业务部门id")
    @TableField("business_dept_id")
    @ApiModelProperty(value = "业务部门id")
    private Long businessDeptId;

    /** 顶级业务部门id */
    @Excel(name = "顶级业务部门id")
    @TableField("business_top_dept_id")
    @ApiModelProperty(value = "顶级业务部门id")
    private Long businessTopDeptId;

    /** 服务类型 */
    @Excel(name = "服务类型")
    @TableField("service_type")
    @ApiModelProperty(value = "服务类型")
    private Integer serviceType;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 客户企业名 */
    @Excel(name = "客户企业名")
    @TableField("customer_company_name")
    @ApiModelProperty(value = "客户企业名")
    private String customerCompanyName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 税号 */
    @Excel(name = "税号")
    @TableField("tax_number")
    @ApiModelProperty(value = "税号")
    private String taxNumber;

    /** 服务编号 */
    @Excel(name = "服务编号")
    @TableField("service_number")
    @ApiModelProperty(value = "服务编号")
    private String serviceNumber;

    /** 首个账期 */
    @Excel(name = "首个账期")
    @TableField("first_account_period")
    @ApiModelProperty(value = "首个账期")
    private Integer firstAccountPeriod;

    /** 结束账期 */
    @Excel(name = "结束账期")
    @TableField("end_account_period")
    @ApiModelProperty(value = "结束账期")
    private Integer endAccountPeriod;

    /** 最后记账账期 */
    @Excel(name = "最后记账账期")
    @TableField("last_account_period")
    @ApiModelProperty(value = "最后记账账期")
    private Integer lastAccountPeriod;

    /** 冻结账期 */
    @Excel(name = "冻结账期")
    @TableField("froze_account_period")
    @ApiModelProperty(value = "冻结账期")
    private Integer frozeAccountPeriod;

    /** 解冻账期 */
    @Excel(name = "解冻账期")
    @TableField(value = "unfroze_account_period", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "解冻账期")
    private Integer unfrozeAccountPeriod;

    /** 纳税人性质，1-小规模，2-一般纳税人 */
    @Excel(name = "纳税人性质，1-小规模，2-一般纳税人")
    @TableField("tax_type")
    @ApiModelProperty(value = "纳税人性质，1-小规模，2-一般纳税人")
    private Integer taxType;

    /** 顾问部门id */
    @Excel(name = "顾问部门id")
    @TableField("advisor_dept_id")
    @ApiModelProperty(value = "顾问部门id")
    private Long advisorDeptId;

    /** 顾问顶级部门id */
    @Excel(name = "顾问顶级部门id")
    @TableField("advisor_top_dept_id")
    @ApiModelProperty(value = "顾问顶级部门id")
    private Long advisorTopDeptId;

    /** 会计部门id */
    @Excel(name = "会计部门id")
//    @TableField(value = "accounting_dept_id", updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "会计部门id")
    private Long accountingDeptId;

    /** 会计顶级部门id */
    @Excel(name = "会计顶级部门id")
    @TableField("accounting_top_dept_id")
    @ApiModelProperty(value = "会计顶级部门id")
    private Long accountingTopDeptId;

    @Excel(name = "服务状态", readConverterExp = "1=服务中,2=已结束,3=冻结中")
    @TableField("service_status")
    @ApiModelProperty(value = "服务状态,1=服务中,2=已结束,3=冻结中")
    private Integer serviceStatus;

    @Excel(name = "新户流转id")
    @TableField("new_customer_id")
    @ApiModelProperty(value = "新户流转id")
    private Long newCustomerId;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    @TableField("this_month_income")
    private BigDecimal thisMonthIncome;

    @TableField("this_month_income_rpa_result")
    private Integer thisMonthIncomeRpaResult;

    @TableField("this_season_income")
    private BigDecimal thisSeasonIncome;

    @TableField("this_season_income_rpa_result")
    private Integer thisSeasonIncomeRpaResult;

    @TableField("this_year_income")
    private BigDecimal thisYearIncome;

    @TableField("this_year_income_rpa_result")
    private Integer thisYearIncomeRpaResult;

    @TableField("this_12_month_income")
    private BigDecimal this12MonthIncome;

    @TableField("this_12_month_income_rpa_result")
    private Integer this12MonthIncomeRpaResult;

    @TableField("ticket_time")
    private LocalDateTime ticketTime;

    @TableField("accounting_remark")
    private String accountingRemark;

    @TableField("advisor_remark")
    private String advisorRemark;

    @TableField("settlement_status")
    @ApiModelProperty(value = "结算状态，1-不可结算，2-可结算，3-未结算，4-已结算")
    private Integer settlementStatus;

    @ApiModelProperty(value = "最后一个利润取数的入账账务id")
    @Excel(name = "最后一个利润取数的入账账务id")
    @TableField("last_in_account_id")
    private Long lastInAccountId;

    /** 注册时间 */
    @Excel(name = "注册时间")
    @TableField("registration_date")
    @ApiModelProperty(value = "注册时间")
    private String registrationDate;

    /** 注册区域 */
    @Excel(name = "注册区域")
    @TableField("registration_region")
    @ApiModelProperty(value = "注册区域")
    private String registrationRegion;

    /** 医社保扣除方式，1-当月报上月，2-当月报当月 */
    @Excel(name = "医社保扣除方式，1-当月报上月，2-当月报当月")
    @TableField("deliver_report_type")
    @ApiModelProperty(value = "医社保扣除方式，1-当月报上月，2-当月报当月")
    private Integer deliverReportType;

    @TableField(exist = false)
    private Boolean hasMedicalSecurityTag;

    @TableField(exist = false)
    private Boolean hasSocialSecurityTag;

    @TableField(exist = false)
    @ApiModelProperty("是否有凭票入账标签")
    private Boolean hasTicketTag;

    @TableField(exist = false)
    @ApiModelProperty(value = "已存在的医保交付单账期")
    private List<Integer> medicalSecurityDeliverPeriods;

    @TableField(exist = false)
    @ApiModelProperty(value = "已存在的社保交付单账期")
    private List<Integer> socialSecurityDeliverPeriods;

    @TableField(exist = false)
    private List<String> sysAccountNames;
}
