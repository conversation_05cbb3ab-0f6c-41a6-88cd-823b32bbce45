package com.bxm.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.bxm.common.core.enums.UserMessageType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.system.api.domain.RemoteUserMessageDTO;
import com.bxm.system.api.domain.RemoteUserMessagePageResult;
import com.bxm.system.api.domain.RemoteUserMessageVO;
import com.bxm.system.api.domain.SysUser;
import com.bxm.system.domain.SysUserMessageFile;
import com.bxm.system.domain.UserMessage;
import com.bxm.system.domain.dto.UserMessageDTO;
import com.bxm.system.domain.dto.UserMessageNotReadInfoDTO;
import com.bxm.system.domain.dto.UserMessageTypeStatisticDTO;
import com.bxm.system.mapper.SysUserMapper;
import com.bxm.system.mapper.UserMessageMapper;
import com.bxm.system.service.ISysUserMessageFileService;
import com.bxm.system.service.IUserMessageService;
import com.google.common.collect.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserMessageServiceImpl extends ServiceImpl<UserMessageMapper, UserMessage> implements IUserMessageService {

    @Autowired
    private UserMessageMapper userMessageMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private ISysUserMessageFileService userMessageFileService;

    @Override
    public Integer getNotReadMessageCount() {
        Long userId = SecurityUtils.getUserId();
        return count(new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false)
                .eq(UserMessage::getIsRead, false));
    }

    @Override
    public IPage<UserMessageDTO> getUserMessageList(Integer pageNum, Integer pageSize) {
        IPage<UserMessageDTO> result = new Page<>();
        Long userId = SecurityUtils.getUserId();
        IPage<UserMessage> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getIsDel, false).eq(UserMessage::getReceiveUserId, userId)
                .orderByDesc(UserMessage::getCreateTime));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            result.setRecords(iPage.getRecords().stream().map(row -> UserMessageDTO.builder()
                    .id(row.getId())
                    .content(row.getContent())
                    .createTime(row.getCreateTime())
                    .sendEmployeeName(row.getSendEmployeeName())
                    .isRead(row.getIsRead())
                    .messageType(row.getMessageType())
                    .build()).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    @Transactional
    public void readMessage(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        updateBatchById(ids.stream().map(row -> new UserMessage().setId(row).setIsRead(true)).collect(Collectors.toList()));
    }

    @Override
    public void allRead(String readTime) {
        Long userId = SecurityUtils.getUserId();
        update(new LambdaUpdateWrapper<UserMessage>()
                .eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false)
                .eq(UserMessage::getIsRead, false)
                .le(UserMessage::getCreateTime, readTime)
                .set(UserMessage::getIsRead, true));
    }

    @Override
    @Transactional
    public void sendMessage(List<RemoteUserMessageVO> voList) {
        if (ObjectUtils.isEmpty(voList)) {
            return;
        }
        saveBatch(voList.stream().map(row -> {
            UserMessage userMessage = new UserMessage();
            BeanUtils.copyProperties(row, userMessage);
            return userMessage;
        }).collect(Collectors.toList()));
    }

    @Override
    public Map<String, Integer> getNotReadMessageCountByUserId(Long userId) {
        Map<String, Integer> result = new HashMap<>();
        if (Objects.isNull(userId)) {
            result.put("urgeNotReadCount", 0);
            result.put("systemNotReadCount", 0);
            return result;
        }
        List<UserMessage> notReadMessageList = list(new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false)
                .eq(UserMessage::getIsRead, false).select(UserMessage::getId, UserMessage::getMessageType));
        result.put("urgeNotReadCount", (int) notReadMessageList.stream().filter(row -> Objects.equals(1, row.getMessageType())).count());
        result.put("systemNotReadCount", (int) notReadMessageList.stream().filter(row -> Objects.equals(2, row.getMessageType())).count());
        return result;
    }

    @Override
    public RemoteUserMessagePageResult getUserMessageListByUserId(Integer pageNum, Integer pageSize, Long userId) {
        IPage<RemoteUserMessageDTO> result = new Page<>();
        IPage<UserMessage> iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getIsRead, false)
                .eq(UserMessage::getIsDel, false).eq(UserMessage::getReceiveUserId, userId)
                .orderByDesc(UserMessage::getCreateTime));
        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            result.setRecords(iPage.getRecords().stream().map(row -> RemoteUserMessageDTO.builder()
                    .id(row.getId())
                    .content(row.getContent())
                    .createTime(row.getCreateTime())
                    .sendEmployeeName(row.getSendEmployeeName())
                    .isRead(row.getIsRead())
                    .messageType(row.getMessageType())
                    .build()).collect(Collectors.toList()));
        }
        return new RemoteUserMessagePageResult(result.getTotal(), result.getCurrent(), result.getSize(), result.getPages(), result.getRecords());
    }

    @Override
    @Transactional
    public void readMessageInner(List<Long> ids, Long userId) {
        if (ObjectUtils.isEmpty(ids)) {
            return;
        }
        List<UserMessage> messages = list(new LambdaQueryWrapper<UserMessage>().in(UserMessage::getId, ids).eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false));
        if (ObjectUtils.isEmpty(messages)) {
            return;
        }
        updateBatchById(messages.stream().map(row -> new UserMessage().setId(row.getId()).setIsRead(true)).collect(Collectors.toList()));
    }

    @Override
    public void allReadInner(Map<String, Object> params) {
        Object userIdObj = params.get("userId");
        Object readTimeObj = params.get("readTime");
        if (Objects.isNull(userIdObj) || Objects.isNull(readTimeObj)) {
            throw new ServiceException("参数错误");
        }
        long userId;
        String readTime;
        try {
            userId = Long.parseLong(userIdObj.toString());
            readTime = readTimeObj.toString();
        } catch (Exception e) {
            throw new ServiceException("参数错误");
        }
        update(new LambdaUpdateWrapper<UserMessage>()
                .eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false)
                .eq(UserMessage::getIsRead, false)
                .le(UserMessage::getCreateTime, readTime)
                .set(UserMessage::getIsRead, true));
    }

    @Override
    public UserMessageNotReadInfoDTO getNotReadMessageInfo() {
        Long userId = SecurityUtils.getUserId();
        List<UserMessage> notReadMessageList = list(new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getReceiveUserId, userId)
                .eq(UserMessage::getIsDel, false)
                .eq(UserMessage::getIsRead, false));
        if (ObjectUtils.isEmpty(notReadMessageList)) {
            return UserMessageNotReadInfoDTO.builder()
                    .notReadMessageCount(0)
                    .notReadMessageTip("")
                    .build();
        }
        Map<Integer, List<UserMessage>> messageMap = notReadMessageList.stream().collect(Collectors.groupingBy(UserMessage::getMessageType));
        StringBuilder sb = new StringBuilder();
        for (UserMessageType value : UserMessageType.values()) {
            List<UserMessage> messages = messageMap.get(value.getCode());
            if (!ObjectUtils.isEmpty(messages)) {
                sb.append(value.getName()).append("未读").append(messages.size()).append("条，");
            }
        }
        return UserMessageNotReadInfoDTO.builder()
                .notReadMessageCount(notReadMessageList.size())
                .notReadMessageTip(StringUtils.isEmpty(sb) ? "" : sb.substring(0, sb.length() - 1))
                .build();
    }

    @Override
    public List<UserMessageTypeStatisticDTO> userMessageTypeStatistic(Integer listType) {
        if (Objects.isNull(listType)) {
            return Collections.emptyList();
        }
        List<UserMessageTypeStatisticDTO> result = Lists.newArrayList();
        if (listType == 1) {
            List<UserMessage> userMessageList = list(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getReceiveUserId, SecurityUtils.getUserId())
                    .eq(UserMessage::getIsDel, false)
                    .eq(UserMessage::getIsRead, false)
                    .select(UserMessage::getMessageType, UserMessage::getId));
            result.add(UserMessageTypeStatisticDTO.builder()
                            .messageType(null)
                            .messageTypeName("全部")
                            .isRead(null)
                            .notReadCount((long) userMessageList.size())
                    .build());
            for (UserMessageType value : UserMessageType.values()) {
                List<UserMessage> messages = userMessageList.stream().filter(row -> Objects.equals(row.getMessageType(), value.getCode())).collect(Collectors.toList());
                result.add(UserMessageTypeStatisticDTO.builder()
                        .messageType(value.getCode())
                        .messageTypeName(value.getName())
                        .isRead(null)
                        .notReadCount((long) messages.size())
                        .build());
            }
        } else {
            List<UserMessage> userMessageList = list(new LambdaQueryWrapper<UserMessage>()
                    .eq(UserMessage::getSendEmployeeName, SecurityUtils.getLoginUser().getSysUser().getNickName())
                    .eq(UserMessage::getIsDel, false)
                    .eq(UserMessage::getIsRead, false)
                    .eq(UserMessage::getMessageType, UserMessageType.ACTIVE_URGE.getCode())
                    .select(UserMessage::getId));
            result.add(UserMessageTypeStatisticDTO.builder()
                            .messageType(UserMessageType.ACTIVE_URGE.getCode())
                            .messageTypeName("全部")
                            .isRead(null)
                            .notReadCount(0L)
                    .build());
            result.add(UserMessageTypeStatisticDTO.builder()
                            .messageType(UserMessageType.ACTIVE_URGE.getCode())
                            .messageTypeName("未读")
                            .isRead(0)
                            .notReadCount((long) userMessageList.size())
                    .build());
        }
        return result;
    }

    @Override
    public IPage<UserMessageDTO> getUserMessageListV2(Integer pageNum, Integer pageSize, Integer messageType, Integer isRead, Integer listType) {
        IPage<UserMessageDTO> result = new Page<>();
        Long userId = SecurityUtils.getUserId();
        IPage<UserMessage> iPage;
        if (listType == 1) {
            iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getIsDel, false).eq(UserMessage::getReceiveUserId, userId)
                .eq(!Objects.isNull(messageType), UserMessage::getMessageType, messageType)
                .eq(!Objects.isNull(isRead), UserMessage::getIsRead, isRead)
                .orderByDesc(UserMessage::getCreateTime));
        } else {
            String currentUserName = SecurityUtils.getLoginUser().getSysUser().getNickName();
            iPage = page(new Page<>(pageNum, pageSize), new LambdaQueryWrapper<UserMessage>()
                .eq(UserMessage::getIsDel, false).eq(UserMessage::getSendEmployeeName, currentUserName)
                .eq(!Objects.isNull(messageType), UserMessage::getMessageType, messageType)
                .eq(!Objects.isNull(isRead), UserMessage::getIsRead, isRead)
                .orderByDesc(UserMessage::getCreateTime));
        }

        BeanUtils.copyProperties(iPage, result);
        if (!ObjectUtils.isEmpty(iPage.getRecords())) {
            List<Long> receiveUserIds = iPage.getRecords().stream().map(UserMessage::getReceiveUserId).collect(Collectors.toList());
            Map<Long, String> userNameMap = sysUserMapper.selectBatchIds(receiveUserIds).stream().collect(Collectors.toMap(SysUser::getUserId, SysUser::getNickName));
            Map<Long, List<SysUserMessageFile>> messageFileMap = userMessageFileService.list(new LambdaQueryWrapper<SysUserMessageFile>().eq(SysUserMessageFile::getIsDel, false)
                            .in(SysUserMessageFile::getMessageId, iPage.getRecords().stream().map(UserMessage::getId).collect(Collectors.toList())))
                    .stream().collect(Collectors.groupingBy(SysUserMessageFile::getMessageId));
            result.setRecords(iPage.getRecords().stream().map(row -> {
                List<SysUserMessageFile> messageFiles = messageFileMap.get(row.getId());
                return UserMessageDTO.builder()
                        .id(row.getId())
                        .content(row.getContent())
                        .createTime(row.getCreateTime())
                        .sendEmployeeName(row.getSendEmployeeName())
                        .isRead(row.getIsRead())
                        .messageType(row.getMessageType())
                        .receiveEmployeeName(userNameMap.getOrDefault(row.getReceiveUserId(), ""))
                        .files(ObjectUtils.isEmpty(messageFiles) ? Lists.newArrayList() :
                                messageFiles.stream().map(f -> CommonFileVO.builder()
                                        .fileUrl(f.getFileUrl())
                                        .fileSize(f.getFileSize())
                                        .fileName(f.getFileName())
                                        .build()).collect(Collectors.toList()))
                        .build();
            }).collect(Collectors.toList()));
        }
        return result;
    }
}
