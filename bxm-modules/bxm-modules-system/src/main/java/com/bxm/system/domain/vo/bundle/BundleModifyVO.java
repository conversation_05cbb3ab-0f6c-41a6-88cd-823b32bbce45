package com.bxm.system.domain.vo.bundle;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BundleModifyVO {

    @ApiModelProperty("套餐id")
    @NotNull(message = "套餐id不能为空")
    private Long id;

    @ApiModelProperty("套餐名称")
    @NotEmpty(message = "套餐名称不能为空")
    private String bundleName;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("所有选中的菜单id")
    private List<Long> menuIds;
}
