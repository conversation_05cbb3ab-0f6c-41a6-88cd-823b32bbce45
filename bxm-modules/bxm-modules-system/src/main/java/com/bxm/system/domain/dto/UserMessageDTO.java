package com.bxm.system.domain.dto;

import com.bxm.common.core.web.domain.CommonFileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UserMessageDTO {

    @ApiModelProperty("消息id")
    private Long id;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("发送人名称")
    private String sendEmployeeName;

    @ApiModelProperty("收件人名称")
    private String receiveEmployeeName;

    @ApiModelProperty("消息内容")
    private String content;

    @ApiModelProperty("是否已读，true-已读，false-未读")
    private Boolean isRead;

    @ApiModelProperty("消息类型，1-催办消息，2-系统消息")
    private Integer messageType;

    @ApiModelProperty("附件列表")
    private List<CommonFileVO> files;
}
