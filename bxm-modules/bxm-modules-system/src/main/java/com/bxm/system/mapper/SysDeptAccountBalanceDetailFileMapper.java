package com.bxm.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.bxm.system.domain.SysDeptAccountBalanceDetailFile;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 业务公司余额变动明细附件Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-09-20
 */
@Mapper
public interface SysDeptAccountBalanceDetailFileMapper extends BaseMapper<SysDeptAccountBalanceDetailFile>
{
    /**
     * 查询业务公司余额变动明细附件
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 业务公司余额变动明细附件
     */
    public SysDeptAccountBalanceDetailFile selectSysDeptAccountBalanceDetailFileById(Long id);

    /**
     * 查询业务公司余额变动明细附件列表
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 业务公司余额变动明细附件集合
     */
    public List<SysDeptAccountBalanceDetailFile> selectSysDeptAccountBalanceDetailFileList(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 新增业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    public int insertSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 修改业务公司余额变动明细附件
     * 
     * @param sysDeptAccountBalanceDetailFile 业务公司余额变动明细附件
     * @return 结果
     */
    public int updateSysDeptAccountBalanceDetailFile(SysDeptAccountBalanceDetailFile sysDeptAccountBalanceDetailFile);

    /**
     * 删除业务公司余额变动明细附件
     * 
     * @param id 业务公司余额变动明细附件主键
     * @return 结果
     */
    public int deleteSysDeptAccountBalanceDetailFileById(Long id);

    /**
     * 批量删除业务公司余额变动明细附件
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysDeptAccountBalanceDetailFileByIds(Long[] ids);
}
