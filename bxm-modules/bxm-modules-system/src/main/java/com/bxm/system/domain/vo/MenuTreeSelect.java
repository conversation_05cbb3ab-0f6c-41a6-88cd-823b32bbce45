package com.bxm.system.domain.vo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MenuTreeSelect implements Serializable {
    private static final long serialVersionUID = 1L;

    /** 节点ID */
    @ApiModelProperty(value = "节点ID")
    private Long id;

    /** 菜单名称 */
    @ApiModelProperty(value = "菜单名称")
    private String menuName;

    /** 菜单类型 */
    @ApiModelProperty(value = "菜单类型，M-菜单，C、F-为功能按钮权限")
    private String menuType;

    /** 权限标识 */
    @ApiModelProperty(value = "权限字符")
    private String perms;

    /** 子节点 */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<MenuTreeSelect> children;

    // Getters and Setters
}