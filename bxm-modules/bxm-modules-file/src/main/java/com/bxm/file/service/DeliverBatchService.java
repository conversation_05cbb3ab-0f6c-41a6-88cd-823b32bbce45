package com.bxm.file.service;

import com.bxm.common.core.constant.CacheConstants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.R;
import com.bxm.common.core.enums.BatchDeliverOperType;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.FullYearClosing;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.redis.service.RedisService;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.RemoteCustomerInAccountService;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.file.bean.dto.*;
import com.bxm.file.factory.ExcelParserFactory;
import com.bxm.file.parser.ExcelParser;
import com.bxm.file.util.ExcelUtils;
import com.bxm.system.api.RemoteDeptService;
import com.bxm.system.api.domain.SysEmployee;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DeliverBatchService {

    private static final Integer THREAD_POOL_SIZE = 20;

    @Autowired
    private ExcelParserFactory excelParserFactory;

    @Autowired
    private FileUploadService fileUploadService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private RemoteCustomerDeliverService remoteCustomerDeliverService;

    @Autowired
    private RemoteDeptService remoteDeptService;

    @Autowired
    private RemoteCustomerInAccountService remoteCustomerInAccountService;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private InAccountConcurrentService inAccountConcurrentService;

    @Value("${templateFileVersion}")
    private String templateFileVersion;

    public String uploadFiles(MultipartFile excelFile, MultipartFile zipFile, MultipartFile personChangeZipFile, Integer deliverType, Integer operType, Integer period, Long deptId) throws Exception {
//        if (!excelFile.getOriginalFilename().contains(templateFileVersion)) {
//            throw new ServiceException("请上传最新的模板文件");
//        }
        // 判断excelFile是否大于3.5M
        if (null != excelFile && excelFile.getSize() > 7 * 1024 * 512) {
            throw new ServiceException("excel文件大小不能超过3.5M");
        }
        Boolean isZip = checkArchiveType(zipFile);
        Boolean personChangeIsZip = checkArchiveType(personChangeZipFile);
        String uuid = UUID.randomUUID().toString().replace("-", "");
        ExcelParser<? extends EnterpriseData> parser = excelParserFactory.getParser(deliverType, operType);
        List<? extends EnterpriseData> dataList = parser.parse(excelFile);
        if (!ObjectUtils.isEmpty(dataList)) {
            dataList.forEach(d -> {
                d.setCheckError("");
                d.setCreditCode(StringUtils.trim(d.getCreditCode()));
            });
        }

        // 将 zip 文件转换为 byte 数组
        byte[] zipBytes = convertToByteArray(zipFile);
        byte[] personChangeZipBytes = convertToByteArray(personChangeZipFile);
        Long userId = SecurityUtils.getUserId();

        // 异步校验和上传数据
        CompletableFuture.runAsync(() -> {
            try {
                fileUploadService.validateAndUploadData(uuid, dataList, zipBytes, period, deptId, userId, operType, deliverType, isZip, personChangeIsZip, personChangeZipBytes);
            } catch (Exception e) {
                log.error(e.getMessage());
            }
        });

        return uuid;
    }

    private Boolean checkArchiveType(MultipartFile zipFile) {
        Boolean isZip = null;
        if (null != zipFile) {
            String fileName = zipFile.getOriginalFilename();
            if (fileName.endsWith(".zip")) {
                isZip = true;
            } else if (fileName.endsWith(".rar")) {
                isZip = false;
            } else {
                throw new ServiceException("附件只支持zip或rar格式");
            }
        }
        return isZip;
    }

    public CheckResult getProgress(String batchNo) {
        CheckResult checkResult = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_CHECK_RESULT + batchNo);
        if (Objects.isNull(checkResult)) {
            checkResult = new CheckResult();
            checkResult.setIsComplete(false);
            Object obj = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_COMPLETE_FILE_COUNT + batchNo);
            Long completeCount = Objects.isNull(obj) ? 0L : Long.parseLong(obj.toString());
            Long totalCount = redisService.getCacheObject(CacheConstants.BATCH_DELIVER_TOTAL_FILE_COUNT + batchNo);
            checkResult.setCompleteFileCount(completeCount);
            checkResult.setTotalFileCount(Objects.isNull(totalCount) ? 0L : totalCount);
            checkResult.setBatchNo(batchNo);
        } else {
            checkResult.setDataList(redisService.getLargeCacheList(CacheConstants.BATCH_DELIVER_CHECK_LIST_RESULT + batchNo, 2000));
        }
        return checkResult;
    }

    public BatchDeliverConfirmResultDTO confirmData(String batchNo, Long deptId) {
        CheckResult checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据未完成解析");
        }
        List<? extends EnterpriseData> successDatas = checkResult.getDataList().stream().filter(row -> StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
        if (ObjectUtils.isEmpty(successDatas)) {
            return BatchDeliverConfirmResultDTO.builder()
                    .totalCount(0)
                    .failCount(0)
                    .successCount(0)
                    .build();
        }
        Long userId = SecurityUtils.getUserId();
        Integer totalCount = successDatas.size();
        AtomicReference<Integer> successCount = new AtomicReference<>(0);
        AtomicReference<Integer> failCount = new AtomicReference<>(0);
        List<SysEmployee> employees = remoteDeptService.getEmployeesBySecondDeptIdAndUserId(deptId, userId).getDataThrowException();
        if (checkResult.getOperType() == 1) {
            switch (checkResult.getDeliverType()) {
                case 1:
                    List<MedicalInsuranceCreateData> medicalInsuranceCreateDataList = successDatas.stream().map(row -> (MedicalInsuranceCreateData) row).collect(Collectors.toList());
                    medicalInsuranceCreateDataList.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setHasPersonChange(StringUtils.isEmpty(row.getPersonnelChangeRemark()) ? 0 : 1);
                            vo.setPersonChangeInfo(row.getPersonnelChangeRemark());
                            vo.setPersonChangeFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(medicalInsuranceCreateDataList);
                    break;
                case 2:
                    List<SocialInsuranceCreateData> socialInsuranceCreateData = successDatas.stream().map(row -> (SocialInsuranceCreateData) row).collect(Collectors.toList());
                    socialInsuranceCreateData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setHasPersonChange(StringUtils.isEmpty(row.getPersonnelChangeRemark()) ? 0 : 1);
                            vo.setPersonChangeInfo(row.getPersonnelChangeRemark());
                            vo.setPersonChangeFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(socialInsuranceCreateData);
                    break;
                case 3:
                    List<PersonTaxCreateData> personTaxCreateData = successDatas.stream().map(row -> (PersonTaxCreateData) row).collect(Collectors.toList());
                    personTaxCreateData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setHasPersonChange(StringUtils.isEmpty(row.getPersonnelChangeRemark()) ? 0 : 1);
                            vo.setPersonChangeInfo(row.getPersonnelChangeRemark());
                            vo.setPersonChangeFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(personTaxCreateData);
                    break;
                case 4:
                    List<CountryTaxCreateData> countryTaxCreateData = successDatas.stream().map(row -> (CountryTaxCreateData) row).collect(Collectors.toList());
                    countryTaxCreateData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setLastYearHouseAmount(row.getHouseAmount());
                            vo.setCreateFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setNoTicketIncome(StringUtils.isEmpty(row.getNoTicketIncome()) ? null : new BigDecimal(row.getNoTicketIncome()));
                            vo.setCreateRemark(row.getRemark());
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(countryTaxCreateData);
                    break;
                case 5:
                    List<PreAuthCreateData> preAuthCreateData = successDatas.stream().map(row -> (PreAuthCreateData) row).collect(Collectors.toList());
                    preAuthCreateData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setNormalTicketAmount(StringUtils.isEmpty(row.getOutNormalTicketAmount()) ? null : new BigDecimal(row.getOutNormalTicketAmount()));
                            vo.setNormalTicketTaxAmount(StringUtils.isEmpty(row.getOutNormalTicketTaxAmount()) ? null : new BigDecimal(row.getOutNormalTicketTaxAmount()));
                            vo.setSpecialTicketAmount(StringUtils.isEmpty(row.getOutSpecialTicketAmount()) ? null : new BigDecimal(row.getOutSpecialTicketAmount()));
                            vo.setSpecialTicketTaxAmount(StringUtils.isEmpty(row.getOutSpecialTicketTaxAmount()) ? null : new BigDecimal(row.getOutSpecialTicketTaxAmount()));
                            vo.setNoTicketAmount(StringUtils.isEmpty(row.getNoTicketIncomeAmount()) ? null : new BigDecimal(row.getNoTicketIncomeAmount()));
                            vo.setNoTicketTaxAmount(StringUtils.isEmpty(row.getNoTicketIncomeTaxAmount()) ? null : new BigDecimal(row.getNoTicketIncomeTaxAmount()));
                            vo.setSimpleAmount(StringUtils.isEmpty(row.getSimpleTaxIncome()) ? null : new BigDecimal(row.getSimpleTaxIncome()));
                            vo.setSimpleTaxAmount(StringUtils.isEmpty(row.getSimpleTaxTaxAmount()) ? null : new BigDecimal(row.getSimpleTaxTaxAmount()));
                            vo.setIncomeTaxAmount(StringUtils.isEmpty(row.getInTaxAmount()) ? null : new BigDecimal(row.getInTaxAmount()));
                            vo.setLastMonthPurposeTaxAmount(StringUtils.isEmpty(row.getLastTaxAmount()) ? null : new BigDecimal(row.getLastTaxAmount()));
                            vo.setCreateRemark(row.getRemark());
                            vo.setCreateFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(preAuthCreateData);
                    break;
                case 6:
                    List<OperatingIncomePersonTaxCreateData> operatingIncomePersonTaxCreateData = successDatas.stream().map(row -> (OperatingIncomePersonTaxCreateData) row).collect(Collectors.toList());
                    operatingIncomePersonTaxCreateData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                            vo.setCustomerServiceId(row.getCustomerServiceId());
                            vo.setDeliverType(checkResult.getDeliverType());
                            vo.setCustomerServicePeriodMonthId(row.getPeriodId());
                            vo.setHasPersonChange(0);
                            vo.setPersonChangeFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setDdl(StringUtils.isEmpty(row.getDdl()) ? "" : DateUtils.localDateToStr(DateUtils.strToLocalDate(row.getDdl(), DateUtils.YYYYMMDD), DateUtils.YYYY_MM_DD));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteCreate(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(operatingIncomePersonTaxCreateData);
                    break;
                default:
                    break;
            }
        } else if (checkResult.getOperType() == 2) {
            switch (checkResult.getDeliverType()) {
                case 1:
                    List<MedicalInsuranceReportData> medicalInsuranceReportData = successDatas.stream().map(row -> (MedicalInsuranceReportData) row).collect(Collectors.toList());
                    medicalInsuranceReportData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(row.getDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()));
                            vo.setOverdueAmount(StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getOverdueAmount()));
                            vo.setSupplementAmount(StringUtils.isEmpty(row.getSupplementAmount()) ? null : new BigDecimal(row.getSupplementAmount()));
                            vo.setReportAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) || StringUtils.isEmpty(row.getSupplementAmount()) || StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()).add(new BigDecimal(row.getSupplementAmount())).add(new BigDecimal(row.getOverdueAmount())));
                            vo.setReportRemark(row.getReportRemark());
                            vo.setReportFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(Objects.equals(row.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReport(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(medicalInsuranceReportData);
                    break;
                case 2:
                    List<SocialInsuranceReportData> socialInsuranceReportData = successDatas.stream().map(row -> (SocialInsuranceReportData) row).collect(Collectors.toList());
                    socialInsuranceReportData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(row.getDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()));
                            vo.setOverdueAmount(StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getOverdueAmount()));
                            vo.setSupplementAmount(StringUtils.isEmpty(row.getSupplementAmount()) ? null : new BigDecimal(row.getSupplementAmount()));
                            vo.setReportAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) || StringUtils.isEmpty(row.getSupplementAmount()) || StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()).add(new BigDecimal(row.getSupplementAmount())).add(new BigDecimal(row.getOverdueAmount())));
                            vo.setReportRemark(row.getReportRemark());
                            vo.setReportFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(Objects.equals(row.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReport(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(socialInsuranceReportData);
                    break;
                case 3:
                    List<PersonTaxReportData> personTaxReportData = successDatas.stream().map(row -> (PersonTaxReportData) row).collect(Collectors.toList());
                    personTaxReportData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(row.getDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()));
                            vo.setOverdueAmount(StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getOverdueAmount()));
                            vo.setSupplementAmount(StringUtils.isEmpty(row.getSupplementAmount()) ? null : new BigDecimal(row.getSupplementAmount()));
                            vo.setReportAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) || StringUtils.isEmpty(row.getSupplementAmount()) || StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()).add(new BigDecimal(row.getSupplementAmount())).add(new BigDecimal(row.getOverdueAmount())));
                            vo.setReportRemark(row.getReportRemark());
                            vo.setReportFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setTaxReportTotalAmount(StringUtils.isEmpty(row.getTaxReportTotalAmount()) ? null : new BigDecimal(row.getTaxReportTotalAmount()));
                            vo.setReportStatus(Objects.equals(row.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReport(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(personTaxReportData);
                    break;
                case 4:
                    List<CountryTaxReportData> countryTaxReportData = successDatas.stream().map(row -> (CountryTaxReportData) row).collect(Collectors.toList());
                    countryTaxReportData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(row.getDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()));
                            vo.setOverdueAmount(StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getOverdueAmount()));
                            vo.setSupplementAmount(StringUtils.isEmpty(row.getSupplementAmount()) ? null : new BigDecimal(row.getSupplementAmount()));
                            vo.setTotalTaxAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) || StringUtils.isEmpty(row.getSupplementAmount()) || StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()).add(new BigDecimal(row.getSupplementAmount())).add(new BigDecimal(row.getOverdueAmount())));
                            vo.setReportRemark(row.getReportRemark());
//                            vo.setAdditionalTaxAmount(StringUtils.isEmpty(row.getAdditionalTaxAmount()) ? null : new BigDecimal(row.getAdditionalTaxAmount()));
//                            vo.setStampDutyTaxAmount(StringUtils.isEmpty(row.getStampTaxAmount()) ? null : new BigDecimal(row.getStampTaxAmount()));
//                            vo.setValueAddTaxAmount(StringUtils.isEmpty(row.getValueAddTaxAmount()) ? null : new BigDecimal(row.getValueAddTaxAmount()));
//                            vo.setOtherTaxAmount(StringUtils.isEmpty(row.getOtherTaxAmount()) ? null : new BigDecimal(row.getOtherTaxAmount()));
                            vo.setReportFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(Objects.equals(row.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReport(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(countryTaxReportData);
                    break;
                case 6:
                    List<OperatingIncomePersonTaxReportData> operatingIncomePersonTaxReportData = successDatas.stream().map(row -> (OperatingIncomePersonTaxReportData) row).collect(Collectors.toList());
                    operatingIncomePersonTaxReportData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverReportVO vo = new RemoteCustomerDeliverReportVO();
                            vo.setId(row.getDeliverId());
                            vo.setCurrentPeriodAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()));
                            vo.setOverdueAmount(StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getOverdueAmount()));
                            vo.setSupplementAmount(StringUtils.isEmpty(row.getSupplementAmount()) ? null : new BigDecimal(row.getSupplementAmount()));
                            vo.setReportAmount(StringUtils.isEmpty(row.getCurrentPeriodAmount()) || StringUtils.isEmpty(row.getSupplementAmount()) || StringUtils.isEmpty(row.getOverdueAmount()) ? null : new BigDecimal(row.getCurrentPeriodAmount()).add(new BigDecimal(row.getSupplementAmount())).add(new BigDecimal(row.getOverdueAmount())));
                            vo.setReportRemark(row.getReportRemark());
                            vo.setReportFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setReportStatus(Objects.equals(row.getReportResult(), "成功") ? 1 : 2);
                            vo.setSaveType(2);
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReport(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(operatingIncomePersonTaxReportData);
                    break;
                default:
                    break;
            }
        } else if (checkResult.getOperType() == 3) {
            switch (checkResult.getDeliverType()) {
                case 1:
                    List<MedicalInsuranceReportExceptionData> medicalInsuranceReportExceptionData = successDatas.stream().map(row -> (MedicalInsuranceReportExceptionData) row).collect(Collectors.toList());
                    medicalInsuranceReportExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(medicalInsuranceReportExceptionData);
                    break;
                case 2:
                    List<SocialInsuranceReportExceptionData> socialInsuranceReportExceptionData = successDatas.stream().map(row -> (SocialInsuranceReportExceptionData) row).collect(Collectors.toList());
                    socialInsuranceReportExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(socialInsuranceReportExceptionData);
                    break;
                case 3:
                    List<PersonTaxReportExceptionData> personTaxReportExceptionData = successDatas.stream().map(row -> (PersonTaxReportExceptionData) row).collect(Collectors.toList());
                    personTaxReportExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setHasPersonChange(StringUtils.isEmpty(row.getHasPersonChange()) ? null : ("是".equals(row.getHasPersonChange()) ? 1 : 0));
                            vo.setPersonChangeInfo(!Objects.isNull(vo.getHasPersonChange()) && vo.getHasPersonChange() == 1 ? row.getPersonChangeInfo() : "");
                            vo.setPersonChangeFiles(!Objects.isNull(vo.getHasPersonChange()) && vo.getHasPersonChange() == 1 ? (ObjectUtils.isEmpty(row.getPersonChangeFiles()) ? Lists.newArrayList() :
                                    row.getPersonChangeFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList())) : Lists.newArrayList());
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(personTaxReportExceptionData);
                    break;
                case 4:
                    List<CountryTaxReportExceptionData> countryTaxReportExceptionData = successDatas.stream().map(row -> (CountryTaxReportExceptionData) row).collect(Collectors.toList());
                    countryTaxReportExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setNoTicketIncome(StringUtils.isEmpty(row.getNoTicketIncome()) ? null : new BigDecimal(row.getNoTicketIncome()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(countryTaxReportExceptionData);
                    break;
                case 6:
                    List<OperatingIncomePersonTaxReportExceptionData> operatingIncomePersonTaxReportExceptionData = successDatas.stream().map(row -> (OperatingIncomePersonTaxReportExceptionData) row).collect(Collectors.toList());
                    operatingIncomePersonTaxReportExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(operatingIncomePersonTaxReportExceptionData);
                    break;
                default:
                    break;
            }
        } else if (checkResult.getOperType() == 4) {
            switch (checkResult.getDeliverType()) {
                case 1:
                    List<MedicalInsuranceDeductionData> medicalInsuranceDeductionData = successDatas.stream().map(row -> (MedicalInsuranceDeductionData) row).collect(Collectors.toList());
                    medicalInsuranceDeductionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                            vo.setId(row.getDeliverId());
                            vo.setDeductionStatus(Objects.equals(row.getDeductionResult(), "成功") ? 1 : 2);
                            vo.setDeductionRemark(row.getDeductionRemark());
                            vo.setDeductionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setSaveType(2);
                            R result = remoteCustomerDeliverService.remoteDeduction(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(medicalInsuranceDeductionData);
                    break;
                case 2:
                    List<SocialInsuranceDeductionData> socialInsuranceDeductionData = successDatas.stream().map(row -> (SocialInsuranceDeductionData) row).collect(Collectors.toList());
                    socialInsuranceDeductionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                            vo.setId(row.getDeliverId());
                            vo.setDeductionStatus(Objects.equals(row.getDeductionResult(), "成功") ? 1 : 2);
                            vo.setDeductionRemark(row.getDeductionRemark());
                            vo.setDeductionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setSaveType(2);
                            R result = remoteCustomerDeliverService.remoteDeduction(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(socialInsuranceDeductionData);
                    break;
                case 3:
                    List<PersonTaxDeductionData> personTaxDeductionData = successDatas.stream().map(row -> (PersonTaxDeductionData) row).collect(Collectors.toList());
                    personTaxDeductionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                            vo.setId(row.getDeliverId());
                            vo.setDeductionStatus(Objects.equals(row.getDeductionResult(), "成功") ? 1 : 2);
                            vo.setDeductionRemark(row.getDeductionRemark());
                            vo.setDeductionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setSaveType(2);
                            R result = remoteCustomerDeliverService.remoteDeduction(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(personTaxDeductionData);
                    break;
                case 4:
                    List<CountryTaxDeductionData> countryTaxDeductionData = successDatas.stream().map(row -> (CountryTaxDeductionData) row).collect(Collectors.toList());
                    countryTaxDeductionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                            vo.setId(row.getDeliverId());
                            vo.setDeductionStatus(Objects.equals(row.getDeductionResult(), "成功") ? 1 : 2);
                            vo.setDeductionRemark(row.getDeductionRemark());
                            vo.setDeductionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setSaveType(2);
                            R result = remoteCustomerDeliverService.remoteDeduction(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(countryTaxDeductionData);
                    break;
                case 6:
                    List<OperatingIncomePersonTaxDeductionData> operatingIncomePersonTaxDeductionData = successDatas.stream().map(row -> (OperatingIncomePersonTaxDeductionData) row).collect(Collectors.toList());
                    operatingIncomePersonTaxDeductionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverDeductionVO vo = new RemoteCustomerDeliverDeductionVO();
                            vo.setId(row.getDeliverId());
                            vo.setDeductionStatus(Objects.equals(row.getDeductionResult(), "成功") ? 1 : 2);
                            vo.setDeductionRemark(row.getDeductionRemark());
                            vo.setDeductionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setSaveType(2);
                            R result = remoteCustomerDeliverService.remoteDeduction(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(operatingIncomePersonTaxDeductionData);
                    break;
                default:
                    break;
            }
        } else if (checkResult.getOperType() == 5) {
            switch (checkResult.getDeliverType()) {
                case 1:
                    List<MedicalInsuranceDeductionExceptionData> medicalInsuranceDeductionExceptionData = successDatas.stream().map(row -> (MedicalInsuranceDeductionExceptionData) row).collect(Collectors.toList());
                    medicalInsuranceDeductionExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(medicalInsuranceDeductionExceptionData);
                    break;
                case 2:
                    List<SocialInsuranceDeductionExceptionData> socialInsuranceDeductionExceptionData = successDatas.stream().map(row -> (SocialInsuranceDeductionExceptionData) row).collect(Collectors.toList());
                    socialInsuranceDeductionExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(socialInsuranceDeductionExceptionData);
                    break;
                case 3:
                    List<PersonTaxDeductionExceptionData> personTaxDeductionExceptionData = successDatas.stream().map(row -> (PersonTaxDeductionExceptionData) row).collect(Collectors.toList());
                    personTaxDeductionExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(personTaxDeductionExceptionData);
                    break;
                case 4:
                    List<CountryTaxDeductionExceptionData> countryTaxDeductionExceptionDataList = successDatas.stream().map(row -> (CountryTaxDeductionExceptionData) row).collect(Collectors.toList());
                    countryTaxDeductionExceptionDataList.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(countryTaxDeductionExceptionDataList);
                    break;
                case 6:
                    List<OperatingIncomePersonTaxDeductionExceptionData> operatingIncomePersonTaxDeductionExceptionData = successDatas.stream().map(row -> (OperatingIncomePersonTaxDeductionExceptionData) row).collect(Collectors.toList());
                    operatingIncomePersonTaxDeductionExceptionData.forEach(row -> {
                        try {
                            RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                            vo.setId(row.getDeliverId());
                            vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setExceptionRemark(row.getRemark());
                            vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                            vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    });
                    checkResult.setDataList(operatingIncomePersonTaxDeductionExceptionData);
                    break;
                default:
                    break;
            }
        } else if (checkResult.getOperType() == 6) {
            if (Objects.equals(checkResult.getDeliverType(), DeliverType.PRE_AUTH.getCode())) {
                List<PreAuthSupplementData> preAuthSupplementData = successDatas.stream().map(row -> (PreAuthSupplementData) row).collect(Collectors.toList());
                preAuthSupplementData.forEach(row -> {
                    try {
                        RemoteCustomerDeliverCreateVO vo = new RemoteCustomerDeliverCreateVO();
                        vo.setId(row.getDeliverId());
                        vo.setThisMonthTaxBurden(row.getTaxStandard());
                        vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                        vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                        vo.setUserId(userId);
                        vo.setDeptId(deptId);
                        R result = remoteCustomerDeliverService.remoteSupplement(vo);
                        if (R.isError(result)) {
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            successCount.getAndSet(successCount.get() + 1);
                        }
                    } catch (Exception e) {
                        row.addCheckError("系统错误");
                        failCount.getAndSet(failCount.get() + 1);
                    }
                });
                checkResult.setDataList(preAuthSupplementData);
            }
        } else if (checkResult.getOperType() == 7) {
            if (Objects.equals(checkResult.getDeliverType(), DeliverType.PRE_AUTH.getCode())) {
                List<PreAuthAuthData> preAuthAuthData = successDatas.stream().map(row -> (PreAuthAuthData) row).collect(Collectors.toList());
                preAuthAuthData.forEach(row -> {
                    try {
                        RemoteCustomerDeliverAuthVO vo = new RemoteCustomerDeliverAuthVO();
                        vo.setId(row.getDeliverId());
                        vo.setAuthStatus(Objects.equals("成功", row.getAuthResult()) ? 1 : 0);
                        vo.setAuthRemark(row.getAuthRemark());
                        RemotePreAuthInfoVO preAuthInfoVO = new RemotePreAuthInfoVO();
                        BeanUtils.copyProperties(row, preAuthInfoVO);
                        vo.setPreAuthInfoVO(preAuthInfoVO);
                        vo.setPreAuthRemind(StringUtils.isEmpty(row.getPreAuthRemind()) ? null : row.getPreAuthRemind());
                        vo.setAuthFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                        vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                        vo.setUserId(userId);
                        vo.setDeptId(deptId);
                        R result = remoteCustomerDeliverService.remoteAuth(vo);
                        if (R.isError(result)) {
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            successCount.getAndSet(successCount.get() + 1);
                        }
                    } catch (Exception e) {
                        row.addCheckError("系统错误");
                        failCount.getAndSet(failCount.get() + 1);
                    }
                });
                checkResult.setDataList(preAuthAuthData);
            }
        } else if (checkResult.getOperType() == 8) {
            if (Objects.equals(checkResult.getDeliverType(), DeliverType.PRE_AUTH.getCode())) {
                List<PreAuthAuthExceptionData> preAuthAuthExceptionData = successDatas.stream().map(row -> (PreAuthAuthExceptionData) row).collect(Collectors.toList());
                preAuthAuthExceptionData.forEach(row -> {
                    try {
                        RemoteCustomerDeliverExceptionVO vo = new RemoteCustomerDeliverExceptionVO();
                        vo.setId(row.getDeliverId());
                        vo.setExceptionStatus(Objects.equals(row.getExceptionResult(), "解除异常") ? 1 : 2);
                        vo.setExceptionRemark(row.getRemark());
                        vo.setExceptionFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setEmployeeId(ObjectUtils.isEmpty(employees) ? null : employees.get(0).getEmployeeId());
                        vo.setEmployeeName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                        vo.setUserId(userId);
                        vo.setDeptId(deptId);
                        R result = remoteCustomerDeliverService.remoteExceptionDeal(vo);
                        if (R.isError(result)) {
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            successCount.getAndSet(successCount.get() + 1);
                        }
                    } catch (Exception e) {
                        row.addCheckError("系统错误");
                        failCount.getAndSet(failCount.get() + 1);
                    }
                });
                checkResult.setDataList(preAuthAuthExceptionData);
            }
        } else if (checkResult.getOperType() == 9) {
            // 补充附件
            List<CommonSupplementReportFileData> supplementReportFileDataList = successDatas.stream().map(row -> (CommonSupplementReportFileData) row).collect(Collectors.toList());
            if (Objects.equals(checkResult.getDeliverType(), DeliverType.PRE_AUTH.getCode())) {
                if (!ObjectUtils.isEmpty(supplementReportFileDataList)) {
                    List<RemoteSupplementReportFilesVO> voList = supplementReportFileDataList.stream().map(row ->
                            RemoteSupplementReportFilesVO.builder().id(row.getDeliverId())
                                    .files(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                            row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()))
                                    .userId(userId)
                                    .deptId(deptId)
                                    .build()).collect(Collectors.toList());
                    try {
                        remoteCustomerDeliverService.remoteSupplementAuthFiles(voList);
                        successCount.getAndSet(successCount.get() + supplementReportFileDataList.size());
                    } catch (ServiceException e) {
                        supplementReportFileDataList.forEach(row -> row.addCheckError(e.getMessage()));
                        failCount.getAndSet(failCount.get() + supplementReportFileDataList.size());
                    }
                }
            } else {
                if (!ObjectUtils.isEmpty(supplementReportFileDataList)) {
                    List<RemoteSupplementReportFilesVO> voList = supplementReportFileDataList.stream().map(row ->
                            RemoteSupplementReportFilesVO.builder().id(row.getDeliverId())
                                    .files(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                            row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()))
                                    .userId(userId)
                                    .deptId(deptId)
                                    .build()).collect(Collectors.toList());
                    try {
                        remoteCustomerDeliverService.remoteSupplementReportFiles(voList);
                        successCount.getAndSet(successCount.get() + supplementReportFileDataList.size());
                    } catch (ServiceException e) {
                        supplementReportFileDataList.forEach(row -> row.addCheckError(e.getMessage()));
                        failCount.getAndSet(failCount.get() + supplementReportFileDataList.size());
                    }
                }
            }
            checkResult.setDataList(supplementReportFileDataList);
        } else if (checkResult.getOperType() == 10) {
            /*List<CustomerServiceInAccountData> inAccountData = successDatas.stream().map(row -> (CustomerServiceInAccountData) row).collect(Collectors.toList());
            inAccountData.forEach(row -> {
                try {
                    RemoteUpdateInAccountV2VO vo = new RemoteUpdateInAccountV2VO();
                    vo.setId(row.getDeliverId());
                    vo.setInTime(Objects.isNull(row.getInTime()) ? null : DateUtils.dateToLocaldateTime(row.getInTime()).toLocalDate());
                    vo.setBankPaymentInputTime(Objects.isNull(row.getBankInTime()) ? null : DateUtils.dateToLocaldateTime(row.getBankInTime()).toLocalDate());
                    if (Objects.isNull(vo.getInTime()) || Objects.isNull(vo.getBankPaymentInputTime())) {
                        vo.setEndTime(null);
                    } else {
                        LocalDate max;
                        if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                            max = vo.getBankPaymentInputTime();
                        } else {
                            max = vo.getInTime();
                        }
                        vo.setEndTime(max);
                    }
                    vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
                    vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
                    vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
                    vo.setRemark(row.getRemark());
                    vo.setUserId(userId);
                    vo.setDealFiles(false);
                    R result = remoteCustomerInAccountService.updateInAccount(deptId, vo);
                    if (R.isError(result)) {
                        row.addCheckError(result.getMsg());
                        failCount.getAndSet(failCount.get() + 1);
                    } else {
                        successCount.getAndSet(successCount.get() + 1);
                    }
                } catch (Exception e) {
                    row.addCheckError("系统错误");
                    failCount.getAndSet(failCount.get() + 1);
                }
            });
            checkResult.setDataList(inAccountData);*/


            //改成并发处理 by mmn
            List<CustomerServiceInAccountData> inAccountData = successDatas.stream().map(row -> (CustomerServiceInAccountData) row).collect(Collectors.toList());
            List<RemoteUpdateInAccountV2VO> vos = Lists.newArrayList();
            /*inAccountData.forEach(row -> {
                RemoteUpdateInAccountV2VO vo = new RemoteUpdateInAccountV2VO();
                vo.setId(row.getDeliverId());
                vo.setInTime(Objects.isNull(row.getInTime()) ? null : DateUtils.dateToLocaldateTime(row.getInTime()).toLocalDate());
                vo.setBankPaymentInputTime(Objects.isNull(row.getBankInTime()) ? null : DateUtils.dateToLocaldateTime(row.getBankInTime()).toLocalDate());
                if (Objects.isNull(vo.getInTime()) || Objects.isNull(vo.getBankPaymentInputTime())) {
                    vo.setEndTime(null);
                } else {
                    LocalDate max;
                    if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                        max = vo.getBankPaymentInputTime();
                    } else {
                        max = vo.getInTime();
                    }
                    vo.setEndTime(max);
                }
                vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
                vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
                vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
                vo.setRemark(row.getRemark());
                vo.setUserId(userId);
                vo.setDealFiles(false);

                vos.add(vo);
            });*/

            for (int i = 0; i < inAccountData.size(); i++) {
                CustomerServiceInAccountData row = inAccountData.get(i);

                RemoteUpdateInAccountV2VO vo = new RemoteUpdateInAccountV2VO();
                vo.setId(row.getDeliverId());
                vo.setInTime(Objects.isNull(row.getInTime()) ? null : DateUtils.dateToLocaldateTime(row.getInTime()).toLocalDate());
                vo.setBankPaymentInputTime(Objects.isNull(row.getBankInTime()) ? null : DateUtils.dateToLocaldateTime(row.getBankInTime()).toLocalDate());
                if (Objects.isNull(vo.getInTime()) || Objects.isNull(vo.getBankPaymentInputTime())) {
                    vo.setEndTime(null);
                } else {
                    LocalDate max;
                    if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
                        max = vo.getBankPaymentInputTime();
                    } else {
                        max = vo.getInTime();
                    }
                    vo.setEndTime(max);
                }
                vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
                vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
                vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
                vo.setRemark(row.getRemark());
                vo.setUserId(userId);
                vo.setDealFiles(false);

                vo.setIndex(i);

                vos.add(vo);

                row.setIndex(i);
            }

            List<R<Integer>> batchUpdateInAccountResults = inAccountConcurrentService.batchUpdateInAccount(deptId, vos);
            /*for (R<Integer> row : batchUpdateInAccountResults) {
                log.info("asdasdasdadasda {}", row);
                log.info("asdasdasdadasdaData {}", row.getData());
            }*/
            Map<Integer, R<Integer>> batchUpdateInAccountResultsMap = batchUpdateInAccountResults.stream().collect(Collectors.toMap(R::getData, r -> r));
            inAccountData.forEach(
                    row -> {
                        R<Integer> thisResult = batchUpdateInAccountResultsMap.get(row.getIndex());

                        if (thisResult == null) {
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            if (R.isError(thisResult)) {
                                row.addCheckError(thisResult.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        }
                    }
            );

            checkResult.setDataList(inAccountData);
        } else if (checkResult.getOperType() == 11) {
            List<ConfirmDeliverData> confirmDeliverData = successDatas.stream().map(row -> (ConfirmDeliverData) row).collect(Collectors.toList());
            if (Objects.equals(checkResult.getDeliverType(), DeliverType.PRE_AUTH.getCode())) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                confirmDeliverData.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemotePreAuthConfirmVO vo = new RemotePreAuthConfirmVO();
                            vo.setId(row.getDeliverId());
                            vo.setRemark(row.getRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remotePreAuthConfirm(vo);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            row.addCheckError(e.getMessage());
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
            } else {
                if (!ObjectUtils.isEmpty(confirmDeliverData)) {
                    List<RemoteConfirmDeliverVO> voList = confirmDeliverData.stream().map(row ->
                            RemoteConfirmDeliverVO.builder().id(row.getDeliverId())
                                    .userId(userId)
                                    .deptId(deptId)
                                    .remark(row.getRemark())
                                    .files(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                            row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()))
                                    .build()).collect(Collectors.toList());
                    remoteCustomerDeliverService.remoteConfirm(voList);
                }
            }
            checkResult.setDataList(confirmDeliverData);
        } else if (checkResult.getOperType() == 12) {
            List<CustomerServiceInAccountV3Data> inAccountData = successDatas.stream().map(row -> (CustomerServiceInAccountV3Data) row).collect(Collectors.toList());
            updateListByCreditCode(inAccountData);

            ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            inAccountData.forEach(row -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        RemoteUpdateInAccountV3VO vo = new RemoteUpdateInAccountV3VO();
                        vo.setId(row.getDeliverId());
                        vo.setBankPaymentInputResultStr(row.getBankPaymentInputResultStr());
                        vo.setInAccountResultStr(row.getInAccountResultStr());
                        vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
                        vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
                        vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
                        vo.setRemark(row.getRemark());
                        vo.setUserId(userId);
                        vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setDealFiles(false);
                        vo.setTaxReportCount(StringUtils.isEmpty(row.getTaxReportCount()) ? null : Integer.parseInt(row.getTaxReportCount()));
                        vo.setTaxReportSalaryTotal(StringUtils.isEmpty(row.getTaxReportSalaryTotal()) ? null : new BigDecimal(row.getTaxReportSalaryTotal()));
                        R result = remoteCustomerInAccountService.updateInAccountV3(deptId, vo);
                        if (R.isError(result)) {
                            row.addCheckError(result.getMsg());
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            successCount.getAndSet(successCount.get() + 1);
                        }
                    } catch (Exception e) {
                        row.addCheckError(e.getMessage());
                        failCount.getAndSet(failCount.get() + 1);
                    }
                }, executorService);
                futures.add(future);
            });

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            executorService.shutdown();

            checkResult.setDataList(inAccountData);

//            List<RemoteUpdateInAccountV3VO> vos = Lists.newArrayList();
//
//            for (int i = 0; i < inAccountData.size(); i++) {
//                CustomerServiceInAccountV3Data row = inAccountData.get(i);
//
//                RemoteUpdateInAccountV3VO vo = new RemoteUpdateInAccountV3VO();
//                vo.setId(row.getDeliverId());
////                vo.setInTime(Objects.isNull(row.getInTime()) ? null : DateUtils.dateToLocaldateTime(row.getInTime()).toLocalDate());
////                vo.setBankPaymentInputTime(Objects.isNull(row.getBankInTime()) ? null : DateUtils.dateToLocaldateTime(row.getBankInTime()).toLocalDate());
//                vo.setBankPaymentInputResultStr(row.getBankPaymentInputResultStr());
//                vo.setInAccountResultStr(row.getInAccountResultStr());
////                if (Objects.isNull(vo.getInTime()) || Objects.isNull(vo.getBankPaymentInputTime())) {
////                    vo.setEndTime(null);
////                } else {
////                    LocalDate max;
////                    if (vo.getBankPaymentInputTime().isAfter(vo.getInTime())) {
////                        max = vo.getBankPaymentInputTime();
////                    } else {
////                        max = vo.getInTime();
////                    }
////                    vo.setEndTime(max);
////                }
//                vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
//                vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
//                vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
//                vo.setRemark(row.getRemark());
//                vo.setUserId(userId);
//                vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
//                vo.setDealFiles(false);
//
//                vo.setIndex(i);
//
//                //新增字段
////                vo.setDeliverResultStr(row.getDeliverResultStr());//正常/无需交付
//                vo.setTaxReportCount(StringUtils.isEmpty(row.getTaxReportCount()) ? null : Integer.parseInt(row.getTaxReportCount()));
//                vo.setTaxReportSalaryTotal(StringUtils.isEmpty(row.getTaxReportSalaryTotal()) ? null : new BigDecimal(row.getTaxReportSalaryTotal()));
//
//                vos.add(vo);
//
//                row.setIndex(i);
//            }
//
////            for (RemoteUpdateInAccountV3VO row : vos) {
////                log.info("canshude index {}", row.getIndex());
////            }
////
////            for (CustomerServiceInAccountV3Data row : inAccountData) {
////                log.info("yuanshide index {}", row.getIndex());
////            }
//
//            List<R<Integer>> batchUpdateInAccountResults = inAccountConcurrentService.batchUpdateInAccountV3(deptId, vos);
////            for (R<Integer> row : batchUpdateInAccountResults) {
////                log.info("asdasdasdadasdaData {}, {}, {}", row.getData(), row.getCode(), row.getMsg());
////            }
//            Map<Integer, R<Integer>> batchUpdateInAccountResultsMap = batchUpdateInAccountResults.stream().filter(row -> row != null && row.getData() != null).collect(Collectors.toMap(R::getData, r -> r));
//            inAccountData.forEach(
//                    row -> {
//                        R<Integer> thisResult = batchUpdateInAccountResultsMap.get(row.getIndex());
//
//                        if (thisResult == null) {
//                            row.addCheckError("系统错误");
//                            failCount.getAndSet(failCount.get() + 1);
//                        } else {
//                            if (R.isError(thisResult)) {
//                                row.addCheckError(StringUtils.isEmpty(thisResult.getMsg()) ? "处理异常" : thisResult.getMsg());
//                                failCount.getAndSet(failCount.get() + 1);
//                            } else {
//                                successCount.getAndSet(successCount.get() + 1);
//                            }
//                        }
//                    }
//            );
        } else if (checkResult.getOperType() == 13) {
            List<CustomerServiceInAccountRpaUpdateData> inAccountData = successDatas.stream().map(row -> (CustomerServiceInAccountRpaUpdateData) row).collect(Collectors.toList());
            updateListByCreditCodeRpa(inAccountData);

            ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
            List<CompletableFuture<Void>> futures = new ArrayList<>();

            inAccountData.forEach(row -> {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    try {
                        RemoteOperateInAccountRpaUpdateVO vo = new RemoteOperateInAccountRpaUpdateVO();
                        vo.setId(row.getDeliverId());

                        vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
                        vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
                        vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
                        vo.setTaxReportCount(StringUtils.isEmpty(row.getTaxReportCount()) ? null : Integer.parseInt(row.getTaxReportCount()));
                        vo.setTaxReportSalaryTotal(StringUtils.isEmpty(row.getTaxReportSalaryTotal()) ? null : new BigDecimal(row.getTaxReportSalaryTotal()));

                        vo.setTableStatusBalance(row.getTableStatusBalance());
                        vo.setRpaSearchTime(Objects.isNull(row.getRpaSearchTime()) ? null : DateUtils.dateToLocaldateTime(row.getRpaSearchTime()));

                        vo.setRpaExeResultStr(row.getRpaExeResultStr());

                        vo.setRpaRemark(row.getRpaRemark());
                        vo.setUserId(userId);
                        vo.setRpaFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                        vo.setDealFiles(false);
                        R result = remoteCustomerInAccountService.inAccountRpaUpdateInner(deptId, vo);
                        if (R.isError(result)) {
                            row.addCheckError(result.getMsg());
                            failCount.getAndSet(failCount.get() + 1);
                        } else {
                            successCount.getAndSet(successCount.get() + 1);
                        }
                    } catch (Exception e) {
                        row.addCheckError(e.getMessage());
                        failCount.getAndSet(failCount.get() + 1);
                    }
                }, executorService);
                futures.add(future);
            });

            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            executorService.shutdown();

            checkResult.setDataList(inAccountData);
//            List<RemoteOperateInAccountRpaUpdateVO> vos = Lists.newArrayList();
//
//            for (int i = 0; i < inAccountData.size(); i++) {
//                CustomerServiceInAccountRpaUpdateData row = inAccountData.get(i);
//
//                RemoteOperateInAccountRpaUpdateVO vo = new RemoteOperateInAccountRpaUpdateVO();
//                vo.setId(row.getDeliverId());
//
//                vo.setMajorIncomeTotal(StringUtils.isEmpty(row.getMajorIncomeTotal()) ? null : new BigDecimal(row.getMajorIncomeTotal()));
//                vo.setMajorCostTotal(StringUtils.isEmpty(row.getMajorCostTotal()) ? null : new BigDecimal(row.getMajorCostTotal()));
//                vo.setProfitTotal(StringUtils.isEmpty(row.getProfitTotal()) ? null : new BigDecimal(row.getProfitTotal()));
//                vo.setTaxReportCount(StringUtils.isEmpty(row.getTaxReportCount()) ? null : Integer.parseInt(row.getTaxReportCount()));
//                vo.setTaxReportSalaryTotal(StringUtils.isEmpty(row.getTaxReportSalaryTotal()) ? null : new BigDecimal(row.getTaxReportSalaryTotal()));
//
//                vo.setTableStatusBalance(row.getTableStatusBalance());
//                //vo.setRpaSearchTime(row.getRpaSearchTime());
//                vo.setRpaSearchTime(Objects.isNull(row.getRpaSearchTime()) ? null : DateUtils.dateToLocaldateTime(row.getRpaSearchTime()));
//
//                vo.setRpaExeResultStr(row.getRpaExeResultStr());
//
//                vo.setRpaRemark(row.getRpaRemark());
//                vo.setUserId(userId);
//                vo.setRpaFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
//                vo.setDealFiles(false);
//
//                vo.setIndex(i);
//
//                vos.add(vo);
//
//                row.setIndex(i);
//            }
//
//            List<R<Integer>> batchInAccountRpaUpdateResults = inAccountConcurrentService.batchInAccountRpaUpdate(deptId, vos);
//            Map<Integer, R<Integer>> batchInAccountRpaUpdateResultsMap = batchInAccountRpaUpdateResults.stream().collect(Collectors.toMap(R::getData, r -> r));
//            inAccountData.forEach(
//                    row -> {
//                        R<Integer> thisResult = batchInAccountRpaUpdateResultsMap.get(row.getIndex());
//
//                        if (thisResult == null) {
//                            row.addCheckError("系统错误");
//                            failCount.getAndSet(failCount.get() + 1);
//                        } else {
//                            if (R.isError(thisResult)) {
//                                row.addCheckError(thisResult.getMsg());
//                                failCount.getAndSet(failCount.get() + 1);
//                            } else {
//                                successCount.getAndSet(successCount.get() + 1);
//                            }
//                        }
//                    }
//            );
        } else if (checkResult.getOperType() == 16) {
            List<RejectDeliverData> rejectDeliverData = successDatas.stream().map(row -> (RejectDeliverData) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(rejectDeliverData)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                rejectDeliverData.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteRejectVO vo = new RemoteRejectVO();
                            vo.setId(row.getDeliverId());
                            vo.setRemark(row.getRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() :
                                    row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteReject(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("远程调用驳回失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(rejectDeliverData);
            }
        } else if (checkResult.getOperType() == 17) {
            List<PreAuthOverData> preAuthOverData = successDatas.stream().map(row -> (PreAuthOverData) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(preAuthOverData)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                preAuthOverData.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteOverDeliverVO vo = new RemoteOverDeliverVO();
                            vo.setId(row.getDeliverId());
                            vo.setOverResult(Objects.equals(row.getOverResult(), "成功") ? 1 : 2);
                            vo.setRemark(row.getOverRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteOverDeliver(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("远程调用完结失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(preAuthOverData);
            }
        } else if (checkResult.getOperType() == 18) {
            List<PreAuthOverExceptionData> preAuthOverExceptionData = successDatas.stream().map(row -> (PreAuthOverExceptionData) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(preAuthOverExceptionData)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                preAuthOverExceptionData.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteDealOverExceptionDeliverVO vo = new RemoteDealOverExceptionDeliverVO();
                            vo.setId(row.getDeliverId());
                            vo.setDealResult(Objects.equals(row.getOverExceptionResult(), "解除异常") ? 1 : 2);
                            vo.setRemark(row.getRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            R result = remoteCustomerDeliverService.remoteDealOverException(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("远程调用解除完结异常失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(preAuthOverExceptionData);
            }
        } else if (checkResult.getOperType() == 19) {
            List<CustomerServiceIncomeUpdateDataDTO> customerServiceIncomeUpdateDataList = successDatas.stream().map(row -> (CustomerServiceIncomeUpdateDataDTO) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(customerServiceIncomeUpdateDataList)) {
                updateListByCreditCodeIncome(customerServiceIncomeUpdateDataList);
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                customerServiceIncomeUpdateDataList.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteCustomerServiceIncomeVO vo = new RemoteCustomerServiceIncomeVO();
                            vo.setId(row.getDeliverId());
                            vo.setAllTicketAmount(StringUtils.isEmpty(row.getAllTicketAmount()) ? null : new BigDecimal(row.getAllTicketAmount()));
                            vo.setAllTicketTaxAmount(StringUtils.isEmpty(row.getAllTicketTaxAmount()) ? null : new BigDecimal(row.getAllTicketTaxAmount()));
                            vo.setTotalInvoiceAmount(StringUtils.isEmpty(row.getTotalInvoiceAmount()) ? null : new BigDecimal(row.getTotalInvoiceAmount()));
                            vo.setTotalInvoiceTaxAmount(StringUtils.isEmpty(row.getTotalInvoiceTaxAmount()) ? null : new BigDecimal(row.getTotalInvoiceTaxAmount()));
                            vo.setNoTicketIncomeAmount(StringUtils.isEmpty(row.getNoTicketIncomeAmount()) ? null : new BigDecimal(row.getNoTicketIncomeAmount()));
                            vo.setTicketTime(Objects.isNull(row.getTicketTime()) ? null : DateUtils.dateToLocaldateTime(row.getTicketTime()));
                            vo.setNoTicketIncomeTime(Objects.isNull(row.getNoTicketIncomeTime()) ? null : DateUtils.dateToLocaldateTime(row.getNoTicketIncomeTime()));
                            vo.setRpaTime(Objects.isNull(row.getRpaTime()) ? null : DateUtils.dateToLocaldateTime(row.getRpaTime()));
                            vo.setRpaResult(StringUtils.isEmpty(row.getRpaResult()) ? null : (Objects.equals("成功", row.getRpaResult()) ? 1 : 2));
                            vo.setRemark(row.getRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            R result = remoteCustomerService.remoteUpdateOrCreateIncome(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("远程调用更新收入失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(customerServiceIncomeUpdateDataList);

                remoteCustomerService.remoteUpdateCustomerIncome(customerServiceIncomeUpdateDataList.stream().map(CustomerServiceIncomeUpdateDataDTO::getCustomerServiceId).distinct().collect(Collectors.toList()), SecurityConstants.INNER);
            }
        } else if (checkResult.getOperType() == 20) {
            List<CustomerServicePeriodYearUpdateData> customerServicePeriodYearUpdateDataList = successDatas.stream().map(row -> (CustomerServicePeriodYearUpdateData) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(customerServicePeriodYearUpdateDataList)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                customerServicePeriodYearUpdateDataList.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteCustomerServicePeriodYearVO vo = new RemoteCustomerServicePeriodYearVO();
                            vo.setId(row.getDeliverId());
                            vo.setLastYearDeductible(StringUtils.isEmpty(row.getLastYearDeductible()) ? null : row.getLastYearDeductible());
                            vo.setPriorYearEstimationNotReversed(StringUtils.isEmpty(row.getPriorYearEstimationNotReversed()) ? null : row.getPriorYearEstimationNotReversed());
                            vo.setPriorYearDepreciationAdjustment(StringUtils.isEmpty(row.getPriorYearDepreciationAdjustment()) ? null : row.getPriorYearDepreciationAdjustment());
                            vo.setFullYearClosing(StringUtils.isEmpty(row.getFullYearClosing()) ? null : FullYearClosing.getByDesc(row.getFullYearClosing()).getCode());
                            vo.setRemark(row.getRemark());
                            vo.setFiles(ObjectUtils.isEmpty(row.getFiles()) ? Lists.newArrayList() : row.getFiles().stream().map(f -> CommonFileVO.builder().fileUrl(f.getUrl()).fileName(f.getFileName()).build()).collect(Collectors.toList()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            R result = remoteCustomerService.remoteUpdateCustomerServicePeriodYear(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("远程调用更新年度汇总失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(customerServicePeriodYearUpdateDataList);
            }
        } else if (checkResult.getOperType() == 21) {
            // 更新个税申报总额
            List<UpdatePersonTaxReportTotalAmountData> updatePersonTaxReportTotalAmountData = successDatas.stream().map(row -> (UpdatePersonTaxReportTotalAmountData) row).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(updatePersonTaxReportTotalAmountData)) {
                ExecutorService executorService = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                updatePersonTaxReportTotalAmountData.forEach(row -> {
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            RemoteUpdateTaxReportTotalAmountVO vo = new RemoteUpdateTaxReportTotalAmountVO();
                            vo.setId(row.getDeliverId());
                            vo.setTaxReportTotalAmount(new BigDecimal(row.getTaxReportTotalAmount()));
                            vo.setUserId(userId);
                            vo.setDeptId(deptId);
                            vo.setOperName(ObjectUtils.isEmpty(employees) ? SecurityUtils.getLoginUser().getSysUser().getNickName() : employees.get(0).getEmployeeName());
                            R result = remoteCustomerDeliverService.remoteUpdateTaxReportTotalAmount(vo, SecurityConstants.INNER);
                            if (R.isError(result)) {
                                row.addCheckError(result.getMsg());
                                failCount.getAndSet(failCount.get() + 1);
                            } else {
                                successCount.getAndSet(successCount.get() + 1);
                            }
                        } catch (Exception e) {
                            log.error("更新个税申报总额失败:{}", e.getMessage());
                            row.addCheckError("系统错误");
                            failCount.getAndSet(failCount.get() + 1);
                        }
                    }, executorService);
                    futures.add(future);
                });

                // 等待所有异步任务完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

                executorService.shutdown();
                checkResult.setDataList(updatePersonTaxReportTotalAmountData);
            }
        }


        if (!ObjectUtils.isEmpty(checkResult.getDataList()) && checkResult.getDataList().stream().anyMatch(c -> !StringUtils.isEmpty(c.getCheckError()))) {
            checkResult.setHasException(true);
        }
        redisService.deleteLargeCacheList(CacheConstants.BATCH_DELIVER_CHECK_LIST_RESULT + checkResult.getBatchNo());
        redisService.setLargeCacheList(CacheConstants.BATCH_DELIVER_CHECK_LIST_RESULT + checkResult.getBatchNo(), checkResult.getDataList(), 2000, 60 * 60L, TimeUnit.SECONDS);
        checkResult.setDataList(Lists.newArrayList());
        redisService.setCacheObject(CacheConstants.BATCH_DELIVER_CHECK_RESULT + checkResult.getBatchNo(), checkResult, 60 * 60L, TimeUnit.SECONDS);
        return BatchDeliverConfirmResultDTO.builder()
                .totalCount(totalCount)
                .failCount(failCount.get())
                .successCount(successCount.get())
                .build();
    }

    public void updateListByCreditCode(List<CustomerServiceInAccountV3Data> inAccountData) {
        if (ObjectUtils.isEmpty(inAccountData)) {
            return;
        }
        Map<String, List<CustomerServiceInAccountV3Data>> duplicateMap = inAccountData.stream()
                .collect(Collectors.groupingBy(CustomerServiceInAccountV3Data::getCreditCode))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!ObjectUtils.isEmpty(duplicateMap)) {
            duplicateMap.forEach((k, v) -> {
                List<CustomerServiceInAccountV3Data> dataList = v.stream().sorted(Comparator.comparing(CustomerServiceInAccountV3Data::getPeriod).reversed()).collect(Collectors.toList());
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        dataList.get(i).setFiles(Collections.emptyList());
                    }
                }
            });
        }
    }

    public void updateListByCreditCodeRpa(List<CustomerServiceInAccountRpaUpdateData> inAccountData) {
        if (ObjectUtils.isEmpty(inAccountData)) {
            return;
        }
        Map<String, List<CustomerServiceInAccountRpaUpdateData>> duplicateMap = inAccountData.stream()
                .collect(Collectors.groupingBy(CustomerServiceInAccountRpaUpdateData::getCreditCode))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!ObjectUtils.isEmpty(duplicateMap)) {
            duplicateMap.forEach((k, v) -> {
                List<CustomerServiceInAccountRpaUpdateData> dataList = v.stream().sorted(Comparator.comparing(CustomerServiceInAccountRpaUpdateData::getPeriod).reversed()).collect(Collectors.toList());
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        dataList.get(i).setFiles(Collections.emptyList());
                    }
                }
            });
        }
    }

    public void updateListByCreditCodeIncome(List<CustomerServiceIncomeUpdateDataDTO> incomeUpdateData) {
        if (ObjectUtils.isEmpty(incomeUpdateData)) {
            return;
        }
        Map<String, List<CustomerServiceIncomeUpdateDataDTO>> duplicateMap = incomeUpdateData.stream()
                .collect(Collectors.groupingBy(CustomerServiceIncomeUpdateDataDTO::getCreditCode))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (!ObjectUtils.isEmpty(duplicateMap)) {
            duplicateMap.forEach((k, v) -> {
                // 按账期数字类型从大到小排序
                List<CustomerServiceIncomeUpdateDataDTO> dataList = v.stream().sorted((o1, o2) -> Integer.parseInt(o2.getPeriod()) - Integer.parseInt(o1.getPeriod())).collect(Collectors.toList());
                for (int i = 0; i < dataList.size(); i++) {
                    if (i != 0) {
                        dataList.get(i).setFiles(Collections.emptyList());
                    }
                }
            });
        }
    }

    public void downloadErrorFile(String batchNo, HttpServletResponse response) {
        CheckResult checkResult = getProgress(batchNo);
        if (!checkResult.getIsComplete()) {
            throw new ServiceException("数据未完成解析");
        }
        if (!checkResult.getHasException()) {
            throw new ServiceException("暂无数据可下载");
        }
        List<? extends EnterpriseData> errorDatas = checkResult.getDataList().stream().filter(row -> !StringUtils.isEmpty(row.getCheckError())).collect(Collectors.toList());
        String dataType = checkResult.getDeliverType() + "-" + checkResult.getOperType();
        String deliverType = "";
        if (checkResult.getDeliverType() <= 6) {
            deliverType = DeliverType.getByCode(checkResult.getDeliverType()).getName();
        } else if (checkResult.getDeliverType() == 7) {
            deliverType = "入账";
        } else if (checkResult.getDeliverType() == 8) {
            deliverType = "结算";
        } else if (checkResult.getDeliverType() == 9) {
            deliverType = "收入";
        } else if (checkResult.getDeliverType() == 10) {
            deliverType = "年度汇总";
        }
        String operType = BatchDeliverOperType.getByCode(checkResult.getOperType()).getName();
        String fileName = "异常数据_" + deliverType + "_" + operType + "_" + checkResult.getPeriod();
        switch (dataType) {
            case "1-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalInsuranceCreateData) row).collect(Collectors.toList()), MedicalInsuranceCreateData.class, fileName);
                break;
            case "1-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalInsuranceReportData) row).collect(Collectors.toList()), MedicalInsuranceReportData.class, fileName);
                break;
            case "1-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalInsuranceReportExceptionData) row).collect(Collectors.toList()), MedicalInsuranceReportExceptionData.class, fileName);
                break;
            case "1-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalInsuranceDeductionData) row).collect(Collectors.toList()), MedicalInsuranceDeductionData.class, fileName);
                break;
            case "1-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (MedicalInsuranceDeductionExceptionData) row).collect(Collectors.toList()), MedicalInsuranceDeductionExceptionData.class, fileName);
                break;
            case "2-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SocialInsuranceCreateData) row).collect(Collectors.toList()), SocialInsuranceCreateData.class, fileName);
                break;
            case "2-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SocialInsuranceReportData) row).collect(Collectors.toList()), SocialInsuranceReportData.class, fileName);
                break;
            case "2-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SocialInsuranceReportExceptionData) row).collect(Collectors.toList()), SocialInsuranceReportExceptionData.class, fileName);
                break;
            case "2-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SocialInsuranceDeductionData) row).collect(Collectors.toList()), SocialInsuranceDeductionData.class, fileName);
                break;
            case "2-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (SocialInsuranceDeductionExceptionData) row).collect(Collectors.toList()), SocialInsuranceDeductionExceptionData.class, fileName);
                break;
            case "3-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PersonTaxCreateData) row).collect(Collectors.toList()), PersonTaxCreateData.class, fileName);
                break;
            case "3-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PersonTaxReportData) row).collect(Collectors.toList()), PersonTaxReportData.class, fileName);
                break;
            case "3-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PersonTaxReportExceptionData) row).collect(Collectors.toList()), PersonTaxReportExceptionData.class, fileName);
                break;
            case "3-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PersonTaxDeductionData) row).collect(Collectors.toList()), PersonTaxDeductionData.class, fileName);
                break;
            case "3-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PersonTaxDeductionExceptionData) row).collect(Collectors.toList()), PersonTaxDeductionExceptionData.class, fileName);
                break;
            case "4-1":
                if (checkResult.getPeriod() % 100 == 12) {
                    ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CountryTaxCreateData) row).collect(Collectors.toList()), CountryTaxCreateData.class, fileName);
                } else {
                    ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> {
                                CountryTaxCreateData data = (CountryTaxCreateData) row;
                                CountryTaxCreate12Data countryTaxCreate12Data = new CountryTaxCreate12Data();
                                BeanUtils.copyProperties(data, countryTaxCreate12Data);
                                return countryTaxCreate12Data;
                            }).collect(Collectors.toList()), CountryTaxCreate12Data.class, fileName);
                }
                break;
            case "4-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CountryTaxReportData) row).collect(Collectors.toList()), CountryTaxReportData.class, fileName);
                break;
            case "4-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CountryTaxReportExceptionData) row).collect(Collectors.toList()), CountryTaxReportExceptionData.class, fileName);
                break;
            case "4-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CountryTaxDeductionData) row).collect(Collectors.toList()), CountryTaxDeductionData.class, fileName);
                break;
            case "4-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CountryTaxDeductionExceptionData) row).collect(Collectors.toList()), CountryTaxDeductionExceptionData.class, fileName);
                break;
            case "5-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthCreateData) row).collect(Collectors.toList()), PreAuthCreateData.class, fileName);
                break;
            case "5-6":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthSupplementData) row).collect(Collectors.toList()), PreAuthSupplementData.class, fileName);
                break;
            case "5-7":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthAuthData) row).collect(Collectors.toList()), PreAuthAuthData.class, fileName);
                break;
            case "5-8":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthAuthExceptionData) row).collect(Collectors.toList()), PreAuthAuthExceptionData.class, fileName);
                break;
            case "6-1":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (OperatingIncomePersonTaxCreateData) row).collect(Collectors.toList()), OperatingIncomePersonTaxCreateData.class, fileName);
                break;
            case "6-2":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (OperatingIncomePersonTaxReportData) row).collect(Collectors.toList()), OperatingIncomePersonTaxReportData.class, fileName);
                break;
            case "6-3":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (OperatingIncomePersonTaxReportExceptionData) row).collect(Collectors.toList()), OperatingIncomePersonTaxReportExceptionData.class, fileName);
                break;
            case "6-4":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (OperatingIncomePersonTaxDeductionData) row).collect(Collectors.toList()), OperatingIncomePersonTaxDeductionData.class, fileName);
                break;
            case "6-5":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (OperatingIncomePersonTaxDeductionExceptionData) row).collect(Collectors.toList()), OperatingIncomePersonTaxDeductionExceptionData.class, fileName);
                break;
            case "1-9":
            case "2-9":
            case "3-9":
            case "4-9":
            case "6-9":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CommonSupplementReportFileData) row).collect(Collectors.toList()), CommonSupplementReportFileData.class, fileName);
                break;
            /*case "1-11":
            case "2-11":
            case "3-11":
            case "4-11":
            case "6-11":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (ConfirmDeliverData) row).collect(Collectors.toList()), ConfirmDeliverData.class, fileName);
                break;*/
            case "7-10":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerServiceInAccountData) row).collect(Collectors.toList()), CustomerServiceInAccountData.class, fileName);
                break;
            case "1-11":
            case "2-11":
            case "3-11":
            case "4-11":
            case "5-11":
            case "6-11":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (ConfirmDeliverData) row).collect(Collectors.toList()), ConfirmDeliverData.class, fileName);
                break;
            case "1-16":
            case "2-16":
            case "3-16":
            case "4-16":
            case "5-16":
            case "6-16":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (RejectDeliverData) row).collect(Collectors.toList()), RejectDeliverData.class, fileName);
                break;
            case "7-12":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerServiceInAccountV3Data) row).collect(Collectors.toList()), CustomerServiceInAccountV3Data.class, fileName);
                break;
            case "7-13":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerServiceInAccountRpaUpdateData) row).collect(Collectors.toList()), CustomerServiceInAccountRpaUpdateData.class, fileName);
                break;
            case "5-17":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthOverData) row).collect(Collectors.toList()), PreAuthOverData.class, fileName);
                break;
            case "5-18":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (PreAuthOverExceptionData) row).collect(Collectors.toList()), PreAuthOverExceptionData.class, fileName);
                break;
            case "9-19":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerServiceIncomeUpdateDataDTO) row).collect(Collectors.toList()), CustomerServiceIncomeUpdateDataDTO.class, fileName);
                break;
            case "10-20":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (CustomerServicePeriodYearUpdateData) row).collect(Collectors.toList()), CustomerServicePeriodYearUpdateData.class, fileName);
                break;
            case "3-21":
                ExcelUtils.exportExcelFile(response, errorDatas.stream().map(row -> (UpdatePersonTaxReportTotalAmountData) row).collect(Collectors.toList()), UpdatePersonTaxReportTotalAmountData.class, fileName);
                break;
            default:
                throw new ServiceException("不支持的类型");
        }
    }

    private byte[] convertToByteArray(MultipartFile file) throws IOException {
        if (null == file || file.isEmpty()) {
            return null;
        }
        try (InputStream inputStream = file.getInputStream();
             ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            return baos.toByteArray();
        }
    }
}
