package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierFlowSupplementFileData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierFlowSupplementFileParser implements ExcelV2Parser<AccountingCashierFlowSupplementFileData> {

    @Override
    public List<AccountingCashierFlowSupplementFileData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierFlowSupplementFileData.class);
    }
}
