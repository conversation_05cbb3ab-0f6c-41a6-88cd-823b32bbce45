package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountingCashierIncomeDealExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountingCashierIncomeDealExceptionParser implements ExcelV2Parser<AccountingCashierIncomeDealExceptionData> {

    @Override
    public List<AccountingCashierIncomeDealExceptionData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountingCashierIncomeDealExceptionData.class);
    }
}
