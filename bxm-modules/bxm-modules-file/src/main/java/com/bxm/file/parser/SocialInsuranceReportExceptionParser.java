package com.bxm.file.parser;

import com.bxm.file.bean.dto.SocialInsuranceReportExceptionData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class SocialInsuranceReportExceptionParser implements ExcelParser<SocialInsuranceReportExceptionData>{
    @Override
    public List<SocialInsuranceReportExceptionData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析社保的 Excel 文件
        return ExcelUtils.parseExcelFile(file, SocialInsuranceReportExceptionData.class);
    }
}
