package com.bxm.file.controller;

import com.bxm.common.core.constant.Constants;
import com.bxm.common.core.constant.SecurityConstants;
import com.bxm.common.core.domain.Result;
import com.bxm.common.core.enums.DeliverFileType;
import com.bxm.common.core.enums.DeliverType;
import com.bxm.common.core.enums.DownloadType;
import com.bxm.common.core.enums.accountingCashier.AccountingCashierType;
import com.bxm.common.core.exception.ServiceException;
import com.bxm.common.core.utils.DateUtils;
import com.bxm.common.core.utils.StringUtils;
import com.bxm.common.core.utils.poi.ExcelUtil;
import com.bxm.common.core.web.domain.CommonFileVO;
import com.bxm.common.security.annotation.InnerAuth;
import com.bxm.common.security.annotation.RequiresPermissions;
import com.bxm.common.security.utils.SecurityUtils;
import com.bxm.customer.api.RemoteCustomerDeliverService;
import com.bxm.customer.api.RemoteCustomerService;
import com.bxm.customer.api.domain.dto.*;
import com.bxm.customer.api.domain.vo.*;
import com.bxm.file.bean.dto.*;
import com.bxm.file.service.AsyncService;
import com.bxm.file.service.IDownloadRecordService;
import com.bxm.file.service.OssService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@RequestMapping("/customer/deliver")
@RestController
@Api(tags = "交付单文件相关")
@Slf4j
public class CustomerDeliverController {

    @Autowired
    private RemoteCustomerDeliverService remoteCustomerDeliverService;

    @Autowired
    private RemoteCustomerService remoteCustomerService;

    @Autowired
    private OssService ossService;

    @Autowired
    private IDownloadRecordService downloadRecordService;

    @Autowired
    private AsyncService asyncService;


    /**
     * 导出医社保交付列表
     */
    @PostMapping("/socialSecurityExport")
    @ApiOperation(value = "导出医社保交付列表，权限字符：customer:deliver:socialSecurityExport", notes = "导出医社保交付列表")
    public void socialSecurityExport(HttpServletResponse response, RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        try {
            vo.setTabType(2);
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
            List<RemoteCustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                    list.stream().map(row -> {
                        RemoteCustomerDeliverSocialSecurityExportDTO dto = new RemoteCustomerDeliverSocialSecurityExportDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList());
            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            Map<String, List<?>> dataMap = new HashMap<>();
            sheetClassMap.put("医社保交付列表", RemoteCustomerDeliverSocialSecurityExportDTO.class);
            dataMap.put("医社保交付列表", exports);
            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "医社保交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    /**
     * 导出医社保交付列表
     */
    @PostMapping("/socialSecurityExportAndUpload")
    public Result socialSecurityExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-医社保（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(2);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_MEDICAL_INSURANCE_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSocialSecurityExportDTO dto = new RemoteCustomerDeliverSocialSecurityExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverSocialSecurityExportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出医社保交付列表
     */
    @PostMapping("/socialSecurityExportAndUploadRetry")
    @ApiIgnore
    public Result socialSecurityExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSocialSecurityExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSocialSecurityExportDTO dto = new RemoteCustomerDeliverSocialSecurityExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverSocialSecurityExportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                log.error("导出文件异常:{}", e.getMessage());
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出个税（工资薪金）交付列表
     */
    @PostMapping("/personalTaxExport")
    @ApiOperation(value = "导出个税（工资薪金）交付列表，权限字符：customer:deliver:personalTaxExport", notes = "导出个税（工资薪金）交付列表")
    public void personalTaxExport(HttpServletResponse response, RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        try {
            vo.setTabType(3);
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
            List<RemoteCustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                    list.stream().map(row -> {
                        RemoteCustomerDeliverPersonalTaxExportDTO dto = new RemoteCustomerDeliverPersonalTaxExportDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList());
            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            Map<String, List<?>> dataMap = new HashMap<>();
            sheetClassMap.put("个税（工资薪金）交付列表", RemoteCustomerDeliverPersonalTaxExportDTO.class);
            dataMap.put("个税（工资薪金）交付列表", exports);
            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "个税（工资薪金）交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    /**
     * 导出个税（工资薪金）交付列表
     */
    @PostMapping("/personalTaxExportAndUpload")
    @ApiOperation(value = "导出个税（工资薪金）交付列表，权限字符：customer:deliver:personalTaxExport", notes = "导出个税（工资薪金）交付列表")
    public Result personalTaxExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-个税（工资薪金）（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(3);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.INCOME_TAX_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverPersonalTaxExportDTO dto = new RemoteCustomerDeliverPersonalTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverPersonalTaxExportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出个税（工资薪金）交付列表
     */
    @PostMapping("/personalTaxExportAndUploadRetry")
    @ApiIgnore
    public Result personalTaxExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverPersonalTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverPersonalTaxExportDTO dto = new RemoteCustomerDeliverPersonalTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverPersonalTaxExportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出个税（经营所得）交付列表
     */
    @PostMapping("/operatingIncomeTaxExport")
    @ApiOperation(value = "导出个税（经营所得）交付列表，权限字符：customer:deliver:operatingIncomeTaxExport", notes = "导出个税（经营所得）交付列表")
    public void operatingIncomeTaxExport(HttpServletResponse response, RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        try {
            vo.setTabType(6);
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
            List<RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                    list.stream().map(row -> {
                        RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO dto = new RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList());
            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            Map<String, List<?>> dataMap = new HashMap<>();
            sheetClassMap.put("个税（经营所得）交付列表", RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO.class);
            dataMap.put("个税（经营所得）交付列表", exports);
            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "个税（经营所得）交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/operatingIncomeTaxExportAndUpload")
    @ApiOperation(value = "导出个税（经营所得）交付列表，权限字符：customer:deliver:operatingIncomeTaxExport", notes = "导出个税（经营所得）交付列表")
    public Result operatingIncomeTaxExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-个税（经营所得）（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(6);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.INCOME_TAX_MANAGEMENT_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO dto = new RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });

        return Result.ok();
    }

    @PostMapping("/operatingIncomeTaxExportAndUploadRetry")
    @ApiIgnore
    public Result operatingIncomeTaxExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO dto = new RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverOperatingIncomePersonTaxExportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出国税交付列表
     */
    @PostMapping("/countryTaxExport")
    @ApiOperation(value = "导出国税交付列表，权限字符：customer:deliver:countryTaxExport", notes = "导出国税交付列表")
    public void countryTaxExport(HttpServletResponse response, RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        try {
            vo.setTabType(4);
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
            List<RemoteCustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                    list.stream().map(row -> {
                        RemoteCustomerDeliverCountryTaxExportDTO dto = new RemoteCustomerDeliverCountryTaxExportDTO();
                        BeanUtils.copyProperties(row, dto);
                        return dto;
                    }).collect(Collectors.toList());
            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            Map<String, List<?>> dataMap = new HashMap<>();
            sheetClassMap.put("国税交付列表", RemoteCustomerDeliverCountryTaxExportDTO.class);
            dataMap.put("国税交付列表", exports);
            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "国税交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/countryTaxExportAndUpload")
    @ApiOperation(value = "导出国税交付列表，权限字符：customer:deliver:countryTaxExport", notes = "导出国税交付列表")
    public Result countryTaxExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-国税（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(4);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_NATIONAL_TAX_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverCountryTaxExportDTO dto = new RemoteCustomerDeliverCountryTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverCountryTaxExportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/countryTaxExportAndUploadRetry")
    @ApiIgnore
    public Result countryTaxExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverCountryTaxExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverCountryTaxExportDTO dto = new RemoteCustomerDeliverCountryTaxExportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverCountryTaxExportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    /**
     * 导出预认证交付列表
     */
    @PostMapping("/preAuthExport")
    @ApiOperation(value = "导出预认证交付列表，权限字符：customer:deliver:preAuthExport", notes = "导出预认证交付列表")
    public void preAuthExport(HttpServletResponse response, RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        try {
            vo.setTabType(5);
            vo.setDeptId(deptId);
            vo.setUserId(SecurityUtils.getUserId());
            List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
            List<RemoteCustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                    list.stream().map(row -> {
                        RemoteCustomerDeliverPreAuthExportDTO dto = new RemoteCustomerDeliverPreAuthExportDTO();
                        RemotePreAuthInfoVO preAuthInfoDTO = row.getPreAuthInfoDTO();
                        BeanUtils.copyProperties(preAuthInfoDTO, dto);
                        BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(RemotePreAuthInfoVO.class).toArray(new String[0]));
                        dto.setRemark(row.getCreateRemark());
                        dto.setHasCreateFiles(!ObjectUtils.isEmpty(row.getFiles()) && row.getFiles().stream().anyMatch(file -> file.getDeliverFileType().equals(DeliverFileType.NEW_PRE_AUTH.getCode())) ? "有" : "无");
                        return dto;
                    }).collect(Collectors.toList());
            Map<String, Class<?>> sheetClassMap = new HashMap<>();
            Map<String, List<?>> dataMap = new HashMap<>();
            sheetClassMap.put("预认证交付列表", RemoteCustomerDeliverPreAuthExportDTO.class);
            dataMap.put("预认证交付列表", exports);
            ossService.downloadFilesAndGenerateZiByFiles(buildFiles(vo, list), dataMap, sheetClassMap, "预认证交付单数据", response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/preAuthExportAndUpload")
    @ApiOperation(value = "导出预认证交付列表")
    public Result preAuthExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-预认证（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(5);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_PRE_AUTH_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverPreAuthExportDTO dto = new RemoteCustomerDeliverPreAuthExportDTO();
                            RemotePreAuthInfoVO preAuthInfoDTO = row.getPreAuthInfoDTO();
                            BeanUtils.copyProperties(preAuthInfoDTO, dto);
                            BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(RemotePreAuthInfoVO.class).toArray(new String[0]));
                            dto.setRemark(row.getCreateRemark());
                            dto.setHasCreateFiles(!ObjectUtils.isEmpty(row.getFiles()) && row.getFiles().stream().anyMatch(file -> file.getDeliverFileType().equals(DeliverFileType.NEW_PRE_AUTH.getCode())) ? "有" : "无");
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverPreAuthExportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/preAuthExportAndUploadRetry")
    @ApiIgnore
    public Result preAuthExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverPreAuthExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverPreAuthExportDTO dto = new RemoteCustomerDeliverPreAuthExportDTO();
                            RemotePreAuthInfoVO preAuthInfoDTO = row.getPreAuthInfoDTO();
                            BeanUtils.copyProperties(preAuthInfoDTO, dto);
                            BeanUtils.copyProperties(row, dto, getPreAuthAllFieldNames(RemotePreAuthInfoVO.class).toArray(new String[0]));
                            dto.setRemark(row.getCreateRemark());
                            dto.setHasCreateFiles(!ObjectUtils.isEmpty(row.getFiles()) && row.getFiles().stream().anyMatch(file -> file.getDeliverFileType().equals(DeliverFileType.NEW_PRE_AUTH.getCode())) ? "有" : "无");
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverPreAuthExportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/settleAccountsExportAndUpload")
    @ApiOperation(value = "导出汇算交付列表")
    public Result settleAccountsExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-汇算（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(7);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_SETTLE_ACCOUNTS_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSettleAccountsDTO dto = new RemoteCustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverSettleAccountsDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/settleAccountsExportAndUploadRetry")
    @ApiIgnore
    public Result settleAccountsExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSettleAccountsDTO dto = new RemoteCustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverSettleAccountsDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/annualReportExportAndUpload")
    @ApiOperation(value = "导出年报交付列表")
    public Result annualReportExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-年报（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(8);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_ANNUAL_REPORT_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverAnnualReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverAnnualReportDTO dto = new RemoteCustomerDeliverAnnualReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverAnnualReportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/annualReportExportAndUploadRetry")
    @ApiIgnore
    public Result annualReportExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverAnnualReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverAnnualReportDTO dto = new RemoteCustomerDeliverAnnualReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverAnnualReportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/residualBenefitsExportAndUpload")
    @ApiOperation(value = "导出残保金交付列表")
    public Result residualBenefitsExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-残保金（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(9);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_RESIDUAL_BENEFITS_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSettleAccountsDTO dto = new RemoteCustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverSettleAccountsDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/residualBenefitsExportAndUploadRetry")
    @ApiIgnore
    public Result residualBenefitsExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverSettleAccountsDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverSettleAccountsDTO dto = new RemoteCustomerDeliverSettleAccountsDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverSettleAccountsDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/timesReportExportAndUpload")
    @ApiOperation(value = "导出次报交付列表")
    public Result timesReportExportAndUpload(RemoteCustomerDeliverSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "交付-次报（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setTabType(10);
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.DELIVERY_TIMES_REPORT_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverTimesReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverTimesReportDTO dto = new RemoteCustomerDeliverTimesReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteCustomerDeliverTimesReportDTO.class);
                dataMap.put(title, exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/timesReportExportAndUploadRetry")
    @ApiIgnore
    public Result timesReportExportAndUploadRetry(@RequestBody RemoteCustomerDeliverSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteCustomerServiceDeliverDTO> list = remoteCustomerDeliverService.remoteList(vo).getDataThrowException();
                List<RemoteCustomerDeliverTimesReportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                        list.stream().map(row -> {
                            RemoteCustomerDeliverTimesReportDTO dto = new RemoteCustomerDeliverTimesReportDTO();
                            BeanUtils.copyProperties(row, dto);
                            return dto;
                        }).collect(Collectors.toList());
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteCustomerDeliverTimesReportDTO.class);
                dataMap.put(vo.getDownloadRecordTitle(), exports);
                asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/accountingCashierExportAndUpload")
    @ApiOperation(value = "导出账务列表（含附件）")
    public Result accountingCashierExportAndUpload(RemoteAccountingCashierSearchVO vo, @RequestHeader("deptId") Long deptId)
    {
        String title = "账务-" + AccountingCashierType.getByCode(vo.getType()).getName() + "（含附件）" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.CUSTOMER_SERVICE_ACCOUNTING_CASHIER_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteAccountingCashierDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteAccountingCashierDTO> l = remoteCustomerService.remoteAccountingCashierList(vo, SecurityConstants.INNER).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    List<AccountingCashierInAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierInAccountExportDTO dto = new AccountingCashierInAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, AccountingCashierInAccountExportDTO.class);
                    dataMap.put(title, exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
                } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    List<AccountingCashierBankExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierBankExportDTO dto = new AccountingCashierBankExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, AccountingCashierBankExportDTO.class);
                    dataMap.put(title, exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
                } else if (Objects.equals(vo.getType(), AccountingCashierType.CHANGE.getCode())) {
                    List<AccountingCashierModifyAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierModifyAccountExportDTO dto = new AccountingCashierModifyAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(downloadRecordId, (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, AccountingCashierModifyAccountExportDTO.class);
                    dataMap.put(title, exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/accountingCashierExportAndUploadRetry")
    @ApiIgnore
    @InnerAuth
    public Result accountingCashierExportAndUploadRetry(@RequestBody RemoteAccountingCashierSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteAccountingCashierDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteAccountingCashierDTO> l = remoteCustomerService.remoteAccountingCashierList(vo, SecurityConstants.INNER).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                if (Objects.equals(vo.getType(), AccountingCashierType.INCOME.getCode())) {
                    List<AccountingCashierInAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierInAccountExportDTO dto = new AccountingCashierInAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(vo.getDownloadRecordTitle(), AccountingCashierInAccountExportDTO.class);
                    dataMap.put(vo.getDownloadRecordTitle(), exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
                } else if (Objects.equals(vo.getType(), AccountingCashierType.FLOW.getCode())) {
                    List<AccountingCashierBankExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierBankExportDTO dto = new AccountingCashierBankExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(vo.getDownloadRecordTitle(), AccountingCashierBankExportDTO.class);
                    dataMap.put(vo.getDownloadRecordTitle(), exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
                } else if (Objects.equals(vo.getType(), AccountingCashierType.CHANGE.getCode())) {
                    List<AccountingCashierModifyAccountExportDTO> exports = ObjectUtils.isEmpty(list) ? Lists.newArrayList() :
                            list.stream().map(row -> {
                                AccountingCashierModifyAccountExportDTO dto = new AccountingCashierModifyAccountExportDTO();
                                BeanUtils.copyProperties(row, dto);
                                return dto;
                            }).collect(Collectors.toList());
                    downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) exports.size());
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(vo.getDownloadRecordTitle(), AccountingCashierModifyAccountExportDTO.class);
                    dataMap.put(vo.getDownloadRecordTitle(), exports);
                    asyncService.uploadExport(buildFiles(vo, list), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/materialFilesExportAndUpload")
    @ApiOperation(value = "材料文件打包下载（异步导出）（传参直接是id的数组，不需要key）,正常的body传参，不是formdata")
    public Result materialFilesExportAndUpload(@RequestBody List<Long> ids)
    {
        if (ObjectUtils.isEmpty(ids)) {
            return Result.fail("请选择要下载的文件");
        }
        String title = "账期材料" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, ids, DownloadType.MATERIAL_FILES_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteMaterialFileSimpleErrorVO> list = remoteCustomerService.getMaterialFilesByIds(ids, SecurityConstants.INNER).getDataThrowException();
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteMaterialFileSimpleErrorVO.class);
                dataMap.put(title, list);
                asyncService.uploadExport(buildFiles(list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/materialFilesExportAndUploadRetry")
    @ApiIgnore
    @InnerAuth
    public Result materialFilesExportAndUploadRetry(@RequestBody MaterialFilesDownloadRetryDTO dto)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteMaterialFileSimpleErrorVO> list = remoteCustomerService.getMaterialFilesByIds(dto.getIds(), SecurityConstants.INNER).getDataThrowException();
                downloadRecordService.updateDataCount(dto.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(dto.getDownloadRecordTitle(), RemoteMaterialFileSimpleErrorVO.class);
                dataMap.put(dto.getDownloadRecordTitle(), list);
                asyncService.uploadExport(buildFiles(list), dataMap, sheetClassMap, dto.getDownloadRecordTitle(), dto.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(dto.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/pushReviewExportAndUpload")
    @ApiOperation(value = "推送异常数据下载（异步导出）（传参直接是batchNo的值，不需要key）,正常的body传参，不是formdata")
    public Result pushReviewExportAndUpload(@RequestBody String batchNo)
    {
        if (StringUtils.isEmpty(batchNo)) {
            return Result.fail("参数错误");
        }
        String title = "推送异常数据" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, batchNo, DownloadType.PUSH_ERROR_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteMaterialPushPreviewListDTO> list = remoteCustomerService.getPushReviewErrorList(batchNo, SecurityConstants.INNER).getDataThrowException();
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(title, RemoteMaterialPushPreviewListDTO.class);
                dataMap.put(title, list);
                asyncService.uploadExport(buildPushPreviewFiles(list), dataMap, sheetClassMap, title, downloadRecordId);
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/pushReviewExportAndUploadRetry")
    @ApiIgnore
    @InnerAuth
    public Result pushReviewExportAndUploadRetry(@RequestBody PushReviewErrorDataRetryDTO dto)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteMaterialPushPreviewListDTO> list = remoteCustomerService.getPushReviewErrorList(dto.getBatchNo(), SecurityConstants.INNER).getDataThrowException();
                downloadRecordService.updateDataCount(dto.getDownloadRecordId(), (long) list.size());
                Map<String, Class<?>> sheetClassMap = new HashMap<>();
                Map<String, List<?>> dataMap = new HashMap<>();
                sheetClassMap.put(dto.getDownloadRecordTitle(), RemoteMaterialPushPreviewListDTO.class);
                dataMap.put(dto.getDownloadRecordTitle(), list);
                asyncService.uploadExport(buildPushPreviewFiles(list), dataMap, sheetClassMap, dto.getDownloadRecordTitle(), dto.getDownloadRecordId());
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(dto.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/workOrderExportAndUpload")
    @ApiOperation("工单导出（异步导出）")
    public Result workOrderExportAndUpload(@RequestHeader("deptId") Long deptId, RemoteWorkOrderSearchVO vo) {
        String title = "工单" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        vo.setDeptId(deptId);
        vo.setUserId(SecurityUtils.getUserId());
        Long downloadRecordId = downloadRecordService.createRecord(title, 0L, 0L, vo, DownloadType.WORK_ORDER_ATTACHMENT);
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteWorkOrderDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteWorkOrderDTO> l = remoteCustomerService.remoteWorkOrderList(vo, SecurityConstants.INNER).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(downloadRecordId, (long) list.size());
                if (StringUtils.isEmpty(vo.getExportTypes())) {
                    ExcelUtil<RemoteWorkOrderDTO> util = new ExcelUtil<>(RemoteWorkOrderDTO.class);
                    asyncService.uploadExport(util, list, title, downloadRecordId);
                } else {
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(title, RemoteWorkOrderDTO.class);
                    dataMap.put(title, list);
                    asyncService.uploadExport(buildWorkOrderFiles(list, vo.getExportTypes()), dataMap, sheetClassMap, title, downloadRecordId);
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(downloadRecordId, e.getMessage());
            }
        });
        return Result.ok();
    }

    @PostMapping("/workOrderExportAndUploadRetry")
    @ApiIgnore
    @InnerAuth
    public Result workOrderExportAndUploadRetry(@RequestBody RemoteWorkOrderSearchVO vo)
    {
        CompletableFuture.runAsync(() -> {
            try {
                List<RemoteWorkOrderDTO> list = Lists.newArrayList();
                Integer pageNum = 1;
                Integer pageSize = 5000;
                vo.setPageSize(pageSize);
                while (true) {
                    vo.setPageNum(pageNum);
                    List<RemoteWorkOrderDTO> l = remoteCustomerService.remoteWorkOrderList(vo, SecurityConstants.INNER).getDataThrowException();
                    if (!ObjectUtils.isEmpty(l)) {
                        list.addAll(l);
                        pageNum++;
                    } else {
                        break;
                    }
                }
                downloadRecordService.updateDataCount(vo.getDownloadRecordId(), (long) list.size());
                if (StringUtils.isEmpty(vo.getExportTypes())) {
                    ExcelUtil<RemoteWorkOrderDTO> util = new ExcelUtil<>(RemoteWorkOrderDTO.class);
                    asyncService.uploadExport(util, list, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
                } else {
                    Map<String, Class<?>> sheetClassMap = new HashMap<>();
                    Map<String, List<?>> dataMap = new HashMap<>();
                    sheetClassMap.put(vo.getDownloadRecordTitle(), RemoteWorkOrderDTO.class);
                    dataMap.put(vo.getDownloadRecordTitle(), list);
                    asyncService.uploadExport(buildWorkOrderFiles(list, vo.getExportTypes()), dataMap, sheetClassMap, vo.getDownloadRecordTitle(), vo.getDownloadRecordId());
                }
            } catch (Exception e) {
                downloadRecordService.updateDownloadError(vo.getDownloadRecordId(), e.getMessage());
            }
        });
        return Result.ok();
    }

    private List<String> getPreAuthAllFieldNames(Class<RemotePreAuthInfoVO> preAuthInfoDTOClass) {
        List<String> fieldNames = new ArrayList<>();
        Field[] fields = preAuthInfoDTOClass.getDeclaredFields();
        for (Field field : fields) {
            fieldNames.add(field.getName());
        }
        return fieldNames;
    }

    private List<CommonFileVO> buildFiles(RemoteCustomerDeliverSearchVO vo, List<RemoteCustomerServiceDeliverDTO> list) {
        if (StringUtils.isEmpty(vo.getDownloadFileTypes())) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(vo.getDownloadFileTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir = getBaseDir(downloadFileType);
            for (RemoteCustomerServiceDeliverDTO dto : list) {
                String dirPath = baseDir + dto.getPeriod() + DeliverType.getByCode(dto.getDeliverType()).getName() + "/" + dto.getCustomerServiceId() + "-" + dto.getCreditCode() + "-" + dto.getCustomerName() + (StringUtils.isEmpty(dto.getTaxCheckType()) ? "" : ("-" + dto.getTaxCheckType()));
                if (downloadFileType == 1) {
                    if (Lists.newArrayList(DeliverType.MEDICAL_INSURANCE.getCode(), DeliverType.SOCIAL_INSURANCE.getCode(), DeliverType.TAX.getCode()).contains(dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.PERSON_CHANGE.getCode();
                    } else if (Objects.equals(DeliverType.TAX_OPERATING_INCOME.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_OPERATING_INCOME_PERSON_TAX.getCode();
                    } else if (Objects.equals(DeliverType.NATIONAL_TAX.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_NATIONAL_TAX.getCode();
                    } else if (Objects.equals(DeliverType.SETTLE_ACCOUNTS.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_SETTLE_ACCOUNTS.getCode();
                    } else if (Objects.equals(DeliverType.ANNUAL_REPORT.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_ANNUAL_REPORT.getCode();
                    } else if (Objects.equals(DeliverType.PRE_AUTH.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_PRE_AUTH.getCode();
                    } else if (Objects.equals(DeliverType.RESIDUAL_BENEFITS.getCode(), dto.getDeliverType())) {
                        downloadFileType = DeliverFileType.NEW_RESIDUAL_BENEFITS.getCode();
                    } else {
                        downloadFileType = DeliverFileType.NEW_TIMES_REPORT.getCode();
                    }
                }
                Integer finalDownloadFileType = downloadFileType;
                List<CommonFileVO> fileList = dto.getFiles().stream().filter(f -> Objects.equals(f.getDeliverFileType(), finalDownloadFileType)).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(fileList)) {
                    for (CommonFileVO file : fileList) {
                        file.setBaseDir(dirPath);
                    }
//                    fileList = Collections.singletonList(CommonFileVO.builder().fileName("").fileUrl("").baseDir(dirPath).build());
                    files.addAll(fileList);
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            dealFileNames(files);
        }
        return files;
    }

    private List<CommonFileVO> buildFiles(RemoteAccountingCashierSearchVO vo, List<RemoteAccountingCashierDTO> list) {
        if (StringUtils.isEmpty(vo.getExportTypes())) {
            return Collections.emptyList();
        }
        List<Integer> downloadFileTypes = Arrays.stream(vo.getExportTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        for (Integer downloadFileType : downloadFileTypes) {
            String baseDir;
            if (downloadFileType == 1) {
                baseDir = "材料";
            } else if (downloadFileType == 2) {
                baseDir = "交付附件";
            } else {
                baseDir = "未知";
            }
            for (RemoteAccountingCashierDTO dto : list) {
                String dirPath = baseDir + dto.getPeriod() + AccountingCashierType.getByCode(vo.getType()).getName() + "/" + dto.getCustomerServiceId() + "-" + dto.getCreditCode() + "-" + dto.getCustomerName();
                List<CommonFileVO> fileList = dto.getFiles().stream().filter(f -> Objects.equals(f.getDeliverFileType(), downloadFileType)).collect(Collectors.toList());
                if (!ObjectUtils.isEmpty(fileList)) {
                    for (CommonFileVO file : fileList) {
                        file.setBaseDir(dirPath);
                    }
//                    fileList = Collections.singletonList(CommonFileVO.builder().fileName("").fileUrl("").baseDir(dirPath).build());
                    files.addAll(fileList);
                }
            }
        }
        if (!ObjectUtils.isEmpty(files)) {
            dealFileNames(files);
        }
        return files;
    }

    @NotNull
    private static String getBaseDir(Integer downloadFileType) {
        String baseDir;
        if (downloadFileType == 1) {
            baseDir = "交付单";
        } else if (downloadFileType == 2) {
            baseDir = "申报";
        } else if (downloadFileType == 3) {
            baseDir = "扣款";
        } else if (downloadFileType == 5) {
            baseDir = "提报";
        } else if (downloadFileType == 6) {
            baseDir = "认证";
        } else if (downloadFileType == 9) {
            baseDir = "检查";
        } else if (downloadFileType == 13) {
            baseDir = "交付";
        } else {
            baseDir = "未知";
        }
        return baseDir;
    }

    private List<CommonFileVO> buildFiles(List<RemoteMaterialFileSimpleErrorVO> list) {
        if (ObjectUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<CommonFileVO> fileList = list.stream().map(f -> CommonFileVO.builder().fileName(f.getFileName()).fileUrl(f.getFileUrl()).baseDir("").build()).collect(Collectors.toList());

        if (!ObjectUtils.isEmpty(fileList)) {
            dealFileNames(fileList);
        }
        return fileList;
    }

    private List<CommonFileVO> buildPushPreviewFiles(List<RemoteMaterialPushPreviewListDTO> list) {
        List<CommonFileVO> files = Lists.newArrayList();
        for (RemoteMaterialPushPreviewListDTO dto : list) {
            String dirPath = dto.getPeriod() + "/" + dto.getMaterialDeliverNumber() + "-" + dto.getCustomerName() + (StringUtils.isEmpty(dto.getBankInfo()) ? "" : ("-" + dto.getBankInfo()));
            for (CommonFileVO file : dto.getFiles()) {
                file.setBaseDir(dirPath);
            }
            files.addAll(dto.getFiles());
        }
        if (!ObjectUtils.isEmpty(files)) {
            dealFileNames(files);
        }
        return files;
    }

    private List<CommonFileVO> buildWorkOrderFiles(List<RemoteWorkOrderDTO> list, String exportTypes) {
        List<Integer> exportTypeList = Arrays.stream(exportTypes.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(list)) {
            list.forEach(row -> {
                if (!ObjectUtils.isEmpty(row.getFiles())) {
                    files.addAll(row.getFiles().stream().filter(f -> exportTypeList.contains(f.getDeliverFileType())).collect(Collectors.toList()));
                }
            });
        }
        if (!ObjectUtils.isEmpty(files)) {
            dealFileNames(files);
        }
        return files;
    }

    private List<CommonFileVO> buildDeliverBatchEmptyFiles(Integer periodMin, Integer periodMax, List<RemoteCustomerPeriodDTO> periodList, String deliverTypes) {
        LocalDate periodStart = LocalDate.parse(periodMin + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDate periodEnd = LocalDate.parse(periodMax + "01", DateTimeFormatter.ofPattern("yyyyMMdd"));
        List<Integer> deliverTypeList = Arrays.stream(deliverTypes.split(",")).map(Integer::valueOf).collect(Collectors.toList());
        List<CommonFileVO> files = Lists.newArrayList();
        while (!periodStart.isAfter(periodEnd)) {
            String time = DateUtils.localDateToStr(periodStart, "yyyy年MM月");
            for (Integer deliverType : deliverTypeList) {
                List<String> operatorList = Objects.equals(deliverType, DeliverType.PRE_AUTH.getCode()) ? Constants.PRE_AUTH_OPERATOR : Constants.OTHER_OPERATOR;
                for (String operator : operatorList) {
                    for (RemoteCustomerPeriodDTO periodDTO : periodList) {
                        String dirPath = time + "/" +
                                DeliverType.getByCode(deliverType).getName() + "/" +
                                operator + "/" +
                                periodDTO.getCustomerServiceId() + "-" + periodDTO.getCreditCode() + "-" + periodDTO.getCustomerName() + "/";
                        files.add(CommonFileVO.builder().fileName("").fileUrl("").baseDir(dirPath).build());
                    }
                }
            }
            periodStart = periodStart.plusMonths(1L);
        }
        return files;
    }

    private List<CommonFileVO> buildDeliverBatchEmptyFilesByWaitDeal(List<RemoteCustomerDeliverMiniDTO> datas) {
        List<CommonFileVO> files = Lists.newArrayList();
        for (RemoteCustomerDeliverMiniDTO data : datas) {
            String dirPath =
                    data.getCustomerServiceId() + "-" + data.getCreditCode() + "-" + data.getCustomerName() + "/";
            files.add(CommonFileVO.builder().fileName("").fileUrl("").baseDir(dirPath).build());
        }
        return files;
    }

    public static void dealFileNames(List<CommonFileVO> files) {
        Map<String, Integer> fileNameCountMap = Maps.newHashMap();
        // 找出files中每个元素file.getBaseDir() + "/" + file.getFileName()有重复的那些元素
        files.stream().collect(Collectors.groupingBy(f -> f.getBaseDir() + "/" + f.getFileName())).forEach((k, v) -> {
            if (v.size() > 1) {
                fileNameCountMap.put(k, 0);
            }
        });
        if (ObjectUtils.isEmpty(fileNameCountMap)) {
            return;
        }
        // 遍历fileNameCountMap，给每个元素file.getBaseDir() + "/" + file.getFileName()加上序号
        files.forEach(f -> {
            if (fileNameCountMap.containsKey(f.getBaseDir() + "/" + f.getFileName())) {
                Integer currentCount = fileNameCountMap.get(f.getBaseDir() + "/" + f.getFileName());
                String[] fileNameArray = f.getFileName().split("\\.");
                if (currentCount > 0) {
                    f.setFileName(fileNameArray[0] + "（" + currentCount + "）" + "." + fileNameArray[1]);
                }
                fileNameCountMap.put(f.getBaseDir() + "/" + f.getFileName(), currentCount + 1);
            }
        });
        dealFileNames(files);
    }

    @PostMapping("/downloadBatchDeliverEmptyZipFileByWaitDeal")
    @ApiOperation(value = "下载批量交付空zip文件(待处理数据)", notes = "下载批量交付空zip文件(待处理数据)")
    public void downloadBatchDeliverEmptyZipFileByWaitDeal(HttpServletResponse response,
                                                           @RequestParam("deliverType") @ApiParam("交付类型，1-医保，2-社保，3-个税（工资薪金），4-国税，5-预认证，6-个税（经营所得）") Integer deliverType,
                                                           @RequestParam("operType") @ApiParam("操作类型，1-新建，2-申报，3-解除申报异常，4-扣款，5-解除扣款异常，6-补充，7-认证，8-解除认证异常") Integer operType,
                                                           @RequestParam("period") Integer period,
                                                           @RequestHeader("deptId") Long deptId) {
        List<RemoteCustomerDeliverMiniDTO> datas = remoteCustomerDeliverService.remoteCustomerDeliverMiniList(deptId, deliverType, operType, period).getDataThrowException(false);
        if (ObjectUtils.isEmpty(datas)) {
            throw new ServiceException("没有需要处理的数据");
        }
        try {
            ossService.downloadFilesAndGenerateZiByFiles(buildDeliverBatchEmptyFilesByWaitDeal(datas), response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }

    @PostMapping("/downloadBatchDeliverEmptyZipFile")
    @ApiOperation(value = "下载批量交付空zip文件，权限字符：customer:deliver:downloadBatchDeliverEmptyZipFile", notes = "下载批量交付空zip文件")
    @RequiresPermissions("customer:deliver:downloadBatchDeliverEmptyZipFile")
    public void downloadBatchDeliverEmptyZipFile(HttpServletResponse response,
                                                 @RequestParam("periodMin") @ApiParam("账期范围最小值，yyyyMM") Integer periodMin,
                                                 @RequestParam("periodMax") @ApiParam("账期范围最大值，yyyyMM") Integer periodMax,
                                                 @RequestParam("deliverTypes") @ApiParam("交付类型，多个用逗号分隔，1-医保，2-社保，3-个税（工资薪金）交付，4-国税，5-预认证，6-个税（经营所得）") String deliverTypes,
                                                 @RequestHeader("deptId") Long deptId) {
        if (StringUtils.isEmpty(deliverTypes) || Objects.isNull(periodMin) || Objects.isNull(periodMax)) {
            throw new ServiceException("下载失败");
        }
        try {
            List<RemoteCustomerPeriodDTO> periodList = remoteCustomerService.getCustomerPeriodByPeriodRange(periodMin, periodMax, deptId, SecurityUtils.getUserId()).getDataThrowException(false);
            ossService.downloadFilesAndGenerateZiByFiles(buildDeliverBatchEmptyFiles(periodMin, periodMax, periodList, deliverTypes), response);
        } catch (Exception e) {
            log.error("导出文件异常:{}", e.getMessage());
            throw new ServiceException("下载失败");
        }
    }
}
