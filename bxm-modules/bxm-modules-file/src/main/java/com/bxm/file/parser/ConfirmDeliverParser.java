package com.bxm.file.parser;

import com.bxm.file.bean.dto.ConfirmDeliverData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class ConfirmDeliverParser implements ExcelParser<ConfirmDeliverData> {
    @Override
    public List<ConfirmDeliverData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, ConfirmDeliverData.class);
    }
}
