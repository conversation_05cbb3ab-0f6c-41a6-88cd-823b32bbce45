package com.bxm.file.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 导出记录 c_download_record
 * 
 * <AUTHOR>
 * @date 2024-05-07
 */
@Data
@ApiModel("导出记录")
@Accessors(chain = true)
@TableName("c_download_record")
public class DownloadRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "id")
    @ApiModelProperty(value = "id")
    private Long id;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 数据量 */
    @Excel(name = "数据量")
    @TableField("data_count")
    @ApiModelProperty(value = "数据量")
    private Long dataCount;

    /** 附件数量 */
    @Excel(name = "附件数量")
    @TableField("attachment_count")
    @ApiModelProperty(value = "附件数量")
    private Long attachmentCount;

    @Excel(name = "状态,0-生成中，1-成功，2-失败")
    @TableField("status")
    @ApiModelProperty(value = "状态,0-生成中，1-成功，2-失败")
    private Integer status;

    @Excel(name = "错误原因")
    @TableField("error_reason")
    @ApiModelProperty(value = "错误原因")
    private String errorReason;

    @Excel(name = "下载类型，1-服务-客户列表，2-服务-操作记录，3-服务-账期列表，4-服务-年度汇总，5-服务-补账服务，6-交付-医社保，7-交付-个税（工资薪金），8-个税（经营所得），9-交付-国税，10-交付-医社保 含附件，11-交付-个税（工资薪金）含附件，12-个税（经营所得）含附件，13-交付-国税 含附件，14-交付-入账，15-收入，16-材料-交接，17-材料借阅")
    @TableField("download_type")
    @ApiModelProperty(value = "下载类型，1-服务-客户列表，2-服务-操作记录，3-服务-账期列表，4-服务-年度汇总，5-服务-补账服务，6-交付-医社保，7-交付-个税（工资薪金），8-个税（经营所得），9-交付-国税，10-交付-医社保 含附件，11-交付-个税（工资薪金）含附件，12-个税（经营所得）含附件，13-交付-国税 含附件，14-交付-入账，15-收入，16-材料-交接，17-材料借阅")
    private Integer downloadType;

    /** 下载地址 */
    @Excel(name = "下载地址")
    @TableField("download_url")
    @ApiModelProperty(value = "下载地址")
    private String downloadUrl;

    @TableField("param")
    @ApiModelProperty(value = "查询参数")
    private String param;

    @TableField("user_id")
    @ApiModelProperty(value = "用户id")
    private Long userId;

    /** 是否删除，0-否，1-是 */
    @Excel(name = "是否删除，0-否，1-是")
    @TableField("is_del")
    @ApiModelProperty(value = "是否删除，0-否，1-是")
    private Boolean isDel;

    @TableField("is_file_del")
    @ApiModelProperty(value = "是否删除文件，0-否，1-是")
    private Boolean isFileDel;
}
