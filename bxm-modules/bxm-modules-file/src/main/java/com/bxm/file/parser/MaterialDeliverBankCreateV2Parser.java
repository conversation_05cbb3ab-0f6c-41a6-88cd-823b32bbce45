package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.MaterialDeliverBankCreateV2Data;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class MaterialDeliverBankCreateV2Parser implements ExcelV2Parser<MaterialDeliverBankCreateV2Data> {

    @Override
    public List<MaterialDeliverBankCreateV2Data> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, MaterialDeliverBankCreateV2Data.class);
    }
}
