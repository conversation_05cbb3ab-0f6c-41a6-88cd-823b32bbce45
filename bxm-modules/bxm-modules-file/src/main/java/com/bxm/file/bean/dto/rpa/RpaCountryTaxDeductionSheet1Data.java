package com.bxm.file.bean.dto.rpa;

import com.bxm.common.core.annotation.Excel;
import com.bxm.file.bean.dto.AliFileDTO;

import java.util.Date;
import java.util.List;

public class RpaCountryTaxDeductionSheet1Data implements RpaEnterpriseData {

    @Excel(name = "序号")
    private String number;

    @Excel(name = "公司名称")
    private String enterpriseName;

    @Excel(name = "纳税识别号")
    private String creditCode;

    @Excel(name = "登入密码")
    private String password;

    @Excel(name = "实名人")
    private String realName;

    @Excel(name = "手机号码")
    private String phone;

    @Excel(name = "扣款金额")
    private String deductionAmount;

    @Excel(name = "执行情况")
    private String deductionResult;

    @Excel(name = "处理时间", dateFormat = "yyyy/M/d HH:mm:ss")
    private Date dealTime;

    @Excel(name = "异常信息")
    private String checkError;

    private List<AliFileDTO> files;

    private Long customerServiceId;

    private Long periodId;

    private Long deliverId;

    private String sheetIndex;

    @Override
    public String getSheetIndex() {
        return sheetIndex;
    }

    public void setSheetIndex(String sheetIndex) {
        this.sheetIndex = sheetIndex;
    }

    public Long getCustomerServiceId() {
        return customerServiceId;
    }

    public void setCustomerServiceId(Long customerServiceId) {
        this.customerServiceId = customerServiceId;
    }

    public Long getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Long periodId) {
        this.periodId = periodId;
    }

    public Long getDeliverId() {
        return deliverId;
    }

    public void setDeliverId(Long deliverId) {
        this.deliverId = deliverId;
    }

    @Override
    public boolean hasErrors() {
        return checkError != null && !checkError.isEmpty();
    }

    @Override
    public void setCheckError(String checkError) {
        this.checkError = checkError;
    }

    @Override
    public void addCheckError(String checkError) {
        if (this.checkError == null || this.checkError.isEmpty()) {
            this.checkError = checkError;
        } else {
            this.checkError += "; " + checkError;
        }
    }

    @Override
    public List<AliFileDTO> getFiles() {
        return files;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getEnterpriseName() {
        return enterpriseName;
    }

    public void setEnterpriseName(String enterpriseName) {
        this.enterpriseName = enterpriseName;
    }

    @Override
    public String getCreditCode() {
        return creditCode;
    }

    @Override
    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getDeductionAmount() {
        return deductionAmount;
    }

    public void setDeductionAmount(String deductionAmount) {
        this.deductionAmount = deductionAmount;
    }

    public String getDeductionResult() {
        return deductionResult;
    }

    public void setDeductionResult(String deductionResult) {
        this.deductionResult = deductionResult;
    }

    public Date getDealTime() {
        return dealTime;
    }

    public void setDealTime(Date dealTime) {
        this.dealTime = dealTime;
    }

    @Override
    public String getCheckError() {
        return checkError;
    }

    public void setFiles(List<AliFileDTO> files) {
        this.files = files;
    }
}
