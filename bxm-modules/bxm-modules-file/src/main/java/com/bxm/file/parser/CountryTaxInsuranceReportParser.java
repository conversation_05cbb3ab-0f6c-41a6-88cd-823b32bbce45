package com.bxm.file.parser;

import com.bxm.file.bean.dto.CountryTaxReportData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CountryTaxInsuranceReportParser implements ExcelParser<CountryTaxReportData>{
    @Override
    public List<CountryTaxReportData> parse(MultipartFile file) throws Exception {
        // 使用 ExcelUtils 解析国税的 Excel 文件
        return ExcelUtils.parseExcelFile(file, CountryTaxReportData.class);
    }
}
