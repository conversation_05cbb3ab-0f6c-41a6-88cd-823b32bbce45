package com.bxm.file.parser;

import com.bxm.file.bean.dto.batchDeliverV2.AccountUserCreateData;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class AccountUserCreateParser implements ExcelV2Parser<AccountUserCreateData> {

    @Override
    public List<AccountUserCreateData> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, AccountUserCreateData.class);
    }
}
