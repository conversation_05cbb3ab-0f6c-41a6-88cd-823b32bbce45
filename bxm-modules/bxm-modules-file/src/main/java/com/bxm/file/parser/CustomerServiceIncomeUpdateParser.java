package com.bxm.file.parser;

import com.bxm.file.bean.dto.CustomerServiceIncomeUpdateDataDTO;
import com.bxm.file.util.ExcelUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Component
public class CustomerServiceIncomeUpdateParser implements ExcelParser<CustomerServiceIncomeUpdateDataDTO> {
    @Override
    public List<CustomerServiceIncomeUpdateDataDTO> parse(MultipartFile file) throws Exception {
        return ExcelUtils.parseExcelFile(file, CustomerServiceIncomeUpdateDataDTO.class);
    }
}
