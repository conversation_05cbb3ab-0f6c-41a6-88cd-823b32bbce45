<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bxm.file.mapper.CustomerRpaDetailFileMapper">
    
    <resultMap type="com.bxm.file.domain.CustomerRpaDetailFile" id="CustomerRpaDetailFileResult">
        <result property="id"    column="id"    />
        <result property="rpaDetailId"    column="rpa_detail_id"    />
        <result property="fileUrl"    column="file_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCustomerRpaDetailFileVo">
        select id, rpa_detail_id, file_url, file_name, create_by, create_time, update_by, update_time from c_customer_rpa_detail_file
    </sql>

    <select id="selectCustomerRpaDetailFileList" parameterType="com.bxm.file.domain.CustomerRpaDetailFile" resultMap="CustomerRpaDetailFileResult">
        <include refid="selectCustomerRpaDetailFileVo"/>
        <where>  
            <if test="rpaDetailId != null "> and rpa_detail_id = #{rpaDetailId}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
        </where>
    </select>
    
    <select id="selectCustomerRpaDetailFileById" parameterType="Long" resultMap="CustomerRpaDetailFileResult">
        <include refid="selectCustomerRpaDetailFileVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCustomerRpaDetailFile" parameterType="com.bxm.file.domain.CustomerRpaDetailFile" useGeneratedKeys="true" keyProperty="id">
        insert into c_customer_rpa_detail_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rpaDetailId != null">rpa_detail_id,</if>
            <if test="fileUrl != null">file_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rpaDetailId != null">#{rpaDetailId},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCustomerRpaDetailFile" parameterType="com.bxm.file.domain.CustomerRpaDetailFile">
        update c_customer_rpa_detail_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="rpaDetailId != null">rpa_detail_id = #{rpaDetailId},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCustomerRpaDetailFileById" parameterType="Long">
        delete from c_customer_rpa_detail_file where id = #{id}
    </delete>

    <delete id="deleteCustomerRpaDetailFileByIds" parameterType="String">
        delete from c_customer_rpa_detail_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>